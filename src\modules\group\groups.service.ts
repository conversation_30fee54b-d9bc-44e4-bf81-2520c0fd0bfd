/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  HttpException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { FindOptionsWhere, In, Like, Repository } from "typeorm";
import { CreateGroupDto } from "./dto/create-group.dto";
import { UpdateGroupDto } from "./dto/update-group.dto";
import { IpGroupEntity } from "./entities/group.entity";
import { SortOrder } from "@/common/filters/sortOrder";
import { to } from "@/common/utils/to";
import { FilterGroupDto } from "./dto/filter-group.dto";
import { getPage, getPageSize, getSkip, getTake, getTotalPage } from "@/common/utils/pagination.utils";

@Injectable()
export class GroupsService {
  constructor(
    @InjectRepository(IpGroupEntity)
    private readonly groupRepository: Repository<IpGroupEntity>
  ) { }

  async create(
    createGroupDto: CreateGroupDto,
    req: any
  ): Promise<IpGroupEntity[]> {
    const createdGroups: IpGroupEntity[] = [];

    const partnerId = req.headers['partner-id'];
    const [ipGroup, ipGroupError] = await to<IpGroupEntity, Error>(this.groupRepository.findOne({ where: { name: createGroupDto.name } }));
    if (ipGroupError) throw new HttpException(ipGroupError.message, HttpStatus.INTERNAL_SERVER_ERROR);
    if (ipGroup) throw new HttpException('Nome do grupo já cadastrado', HttpStatus.BAD_REQUEST);
      const group = this.groupRepository.create({
        ...createGroupDto,
        created_by: req.user.email,
        customers_id: partnerId,
        created_at: new Date(),
        is_activated: createGroupDto.is_activated,
      });

      try {
        const savedGroup = await this.groupRepository.save(group);

        createdGroups.push(savedGroup);
      } catch (error) {
        throw new Error(
          `Error saving group for customer ID: ${error}`
        );
      }

    return createdGroups;
  }

  async findAll(filter: FilterGroupDto): Promise<{
    data: Array<IpGroupEntity>;
    totalItems: number;
    totalPages: number;
    currentPage: number;
    pageSize: number;
  }> {
    if (!filter.sortOrder) {
      filter.sortOrder = SortOrder.DESC;
    }

    const page = getPage(filter?.page);
    const pageSize = getPageSize(filter?.pageSize);
    const customers = filter.customers ? filter.customers.split(",") : [""];

    const where: FindOptionsWhere<IpGroupEntity> | FindOptionsWhere<IpGroupEntity>[] = {
      is_activated: filter.is_activated,
      customers_id: In(customers)
    }

    if (filter.name) {
      where.name = Like(`%${filter.name}%`)
    }

    const totalItems = await this.groupRepository.count({ where });

    const totalPages = getTotalPage(totalItems ,pageSize);

    const skip = getSkip((page - 1) * pageSize, totalItems)
    const take = getTake(pageSize, totalItems, skip)

    const [data, error] = await to<IpGroupEntity[], Error>(
      this.groupRepository.find({
        relations: {
          ips: true,
          customer: true,
        },
        skip,
        take,
        where,
        order: { created_at: filter.sortOrder },
      })
    );

    if (error)
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);

    return {
      data,
      totalItems,
      totalPages,
      currentPage: page,
      pageSize,
    };
  }

  async findOne(id: string): Promise<IpGroupEntity> {
    const group = await this.groupRepository.findOne({
      where: { id },
      relations: {
        ips: true,
        customer: true,
      },
    });
    if (!group) {
      throw new NotFoundException("Group not found");
    }
    return group;
  }

  async update(
    id: string,
    { is_activated, ...updateGroupDto }: UpdateGroupDto,
    req: any
  ): Promise<IpGroupEntity> {
    await this.findOne(id); // check if exists

    await this.groupRepository.update(id, {
      ...updateGroupDto,
      created_by: req.user.email,
      updated_at: new Date(),
      is_activated: is_activated,
    });

    return this.findOne(id);
  }

  async remove(id: string): Promise<void> {
    const result = await this.groupRepository.softDelete(id);
    if (result.affected === 0) {
      throw new NotFoundException("Group not found");
    }
  }
}
