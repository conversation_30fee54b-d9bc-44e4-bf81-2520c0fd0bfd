import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToMany,
  JoinTable,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
} from "typeorm";
import { PermissionsEntity } from "@/modules/permissions/entities/permissions.entity";
import { UserEntity } from "@/modules/users/entities/user.entity";

@Entity("permission_groups")
export class GroupEntity {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column()
  name: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt?: Date;

  @ManyToMany(() => UserEntity, (user) => user.permissionGroups)
  user?: UserEntity[];

  @ManyToMany(() => PermissionsEntity, (permissions) => permissions.groups, {
    eager: false,
  })
  @JoinTable({
    name: "permissions_permission_groups",
    joinColumn: {
      name: "permission_groups_id",
      referencedColumnName: "id",
    },
    inverseJoinColumn: {
      name: "permissions_id",
      referencedColumnName: "id",
    },
  })
  permissions?: PermissionsEntity[];

  @Column()
  customers_id?: string;

  @DeleteDateColumn()
  deletedAt?: Date;
}
