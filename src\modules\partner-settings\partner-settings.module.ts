import { Modu<PERSON> } from "@nestjs/common";
import { HttpModule } from "@nestjs/axios";
import { PartnerSettingsService } from "./partner-settings.service";
import { TypeOrmModule } from "@nestjs/typeorm";
import { PartnerSecuritySettings } from "./entities/security-settings.entity";
import { PartnerIpRestrictions } from "./entities/ip-restrictions.entity";
import { DatabasePamModule } from "@/common/database/pam/database.module";

@Module({
  imports: [
    DatabasePamModule,
    TypeOrmModule.forFeature([PartnerSecuritySettings, PartnerIpRestrictions], "partnersConnection"),
    HttpModule,
  ],
  providers: [PartnerSettingsService],
  exports: [PartnerSettingsService],
})
export class PartnerSettingsModule {}
