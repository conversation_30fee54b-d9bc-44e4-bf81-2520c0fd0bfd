import { Module } from "@nestjs/common";
import { CustomersService } from "./customers.service";
import { CustomersController } from "./customers.controller";
import { DatabaseModule } from "@/common/database/users/database.module";
import { AuthenticationModule } from "../authentication/authentication.module";
import { UsersModule } from "../users/users.module";

@Module({
  imports: [DatabaseModule, AuthenticationModule, UsersModule],
providers: [
    CustomersService,
  ],
  controllers: [CustomersController],
  exports: [CustomersService],
})
export class CustomersModule {}
