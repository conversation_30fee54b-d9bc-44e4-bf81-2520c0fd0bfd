import {
  IsString,
  IsOptional,
  <PERSON><PERSON>ot<PERSON>mpty,
  <PERSON>Date,
  <PERSON><PERSON><PERSON><PERSON>,
} from "class-validator";
import { Type } from "class-transformer";
import { PermissionsDto } from "@/modules/permissions/dto/permissions.dto";
import { UserDto } from "@/modules/users/dtos/user.dto";

export class UpdateGroupDto {
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  id: string;

  @IsString()
  @IsOptional()
  name: string;

  @IsOptional()
  @Type(() => PermissionsDto)
  permissions?: PermissionsDto[];

  @IsDate()
  @IsOptional()
  deletedAt?: Date;

  @IsDate()
  @IsOptional()
  createdAt?: Date;

  @IsDate()
  @IsOptional()
  updatedAt?: Date;

  @IsArray()
  @IsOptional()
  users?: UserDto[];

  @IsString()
  @IsOptional()
  customers_id?: string;
}
