import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyTo<PERSON>ne, PrimaryColumn, PrimaryGeneratedColumn } from "typeorm";
import { CustomerUserEntity } from "./customer-user.entity";
import { IpGroupEntity } from "@/modules/group/entities/group.entity";
import { UserEntity } from "@/modules/users/entities/user.entity";

@Entity('users_customers_has_ip_groups')
export class UsersCustomersHasIpGroupsEntity {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @ManyToOne(() => IpGroupEntity, (group) => group.id)
    @JoinColumn({ name: "ip_groups_id" })
    ipGroup: IpGroupEntity;

    @ManyToOne(() => UserEntity, (user) => user.id)
    @JoinColumn({ name: "users_customers_id" })
    customerUser: CustomerUserEntity
}