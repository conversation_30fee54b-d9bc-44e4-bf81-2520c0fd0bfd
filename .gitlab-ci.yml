stages:
  - deploy-image
  - update-gitops

services:
  - name: public.ecr.aws/docker/library/docker:26-dind
    alias: docker

variables:
  DOCKER_DRIVER: overlay2


deploy-image:
  stage: deploy-image
  environment: $CI_COMMIT_REF_NAME
  tags:
    - h2
  image: public.ecr.aws/debian/debian:bullseye-slim
  only:
    refs:
      - development
      - qa
      - staging
      - production
  before_script:
    - apt-get update -qq
    - apt-get install -y awscli docker.io curl
    - mkdir -vp ~/.docker/cli-plugins/ ~/dockercache
    - curl --silent -L "https://github.com/docker/buildx/releases/download/v0.13.1/buildx-v0.13.1.linux-arm64" > ~/.docker/cli-plugins/docker-buildx
    - chmod a+x ~/.docker/cli-plugins/docker-buildx
  script:
    - source /root/.bashrc
    - docker buildx create --use
    - aws ecr get-login-password --region $AWS_DEFAULT_REGION | docker login --username AWS --password-stdin $ECR_URI_BASE
    - docker buildx build --push --platform linux/arm64 -f ./Dockerfile -t $ECR_URI_BASE/$CI_COMMIT_REF_NAME/pam/user-admin:$CI_COMMIT_SHA .
    - echo "Build e publicação ok!"

update-gitops:
  stage: update-gitops
  environment: $CI_COMMIT_REF_NAME
  tags:
    - h2
  image: public.ecr.aws/debian/debian:bullseye-slim
  needs:
    - deploy-image
  only:
    refs:
      - development
      - qa
      - staging
      - production
  script:
    - apt-get update
    - apt-get install git wget -y
    - wget https://github.com/mikefarah/yq/releases/latest/download/yq_linux_arm64 -O /usr/bin/yq && chmod +x /usr/bin/yq
    - echo "Clonando o repositório..."
    - |
      git clone https://${GITOPS_REPOSITORY_USER}:${GITOPS_REPOSITORY_ACCESS_TOKEN}@gitlab.opah.com.br/h2/pam/gitops/template-pam-user-admin.git template-pam-user-admin
    - cd template-pam-user-admin
    - git checkout $CI_COMMIT_REF_NAME
    - |
      yq e ".spec.template.spec.containers[0].image = \"${ECR_URI_BASE}/${CI_COMMIT_REF_NAME}/pam/user-admin:${CI_COMMIT_SHA}\"" -i deployment.yml
    - git config --global user.email "<EMAIL>"
    - git config --global user.name "Administrator"
    - git add .
    - git commit -m "Atualizando imagem docker"
    - echo "Fazendo push das alterações..."
    - |
      git push https://${GITOPS_REPOSITORY_USER}:${GITOPS_REPOSITORY_ACCESS_TOKEN}@gitlab.opah.com.br/h2/pam/gitops/template-pam-user-admin.git $CI_COMMIT_REF_NAME