import { IsOptional, Matches } from "class-validator";

export class ChangeEmailPasswordDto {
  @Matches(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[$&+,:;=?@#|'<>.^*()%!-])[A-Za-z\d@$&+,:;=?@#|'<>.^*()%!-]{8,}$/,
    { message: "invalid password" },
  )
  newPassword: string;

    @Matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[$&+,:;=?@#|'<>.^*()%!-])[A-Za-z\d@$&+,:;=?@#|'<>.^*()%!-]{8,}$/,
      { message: 'invalid password' },
    )
    confirmNewPassword: string;

    @IsOptional()
    resetCode:string
}
