import { HttpException, HttpStatus, Injectable, Logger } from '@nestjs/common';
import { UserEntity } from '@/modules/users/entities/user.entity';
import {
  EntityManager,
  InsertResult,
  UpdateResult,
  DeleteResult,
  In,
  Like,
} from 'typeorm';
import { compare, hash } from 'bcrypt';
import { GenericFilter } from '@/common/filters/dtos/generic-filter.dto';
import { UserFilter } from '@/common/filters/interface/userFilter.interface';
import { SortOrder } from '@/common/filters/sortOrder';
import { UserDto } from '@/modules/users/dtos/user.dto';
import { UpdateUserDto } from '@/modules/users/dtos/update-user.dto';
import { to } from '@/common/utils/to';
import { InjectEntityManager } from '@nestjs/typeorm';
import { LogEntity } from '../logHistory/entities/log.entity';
import { CustomersService } from '../customers/customers.service';
import { ChangePasswordDto } from './dtos/change-password.dto';
import { IRequestWithUser } from './interface/request-user.interface';
import { CustomerUserEntity } from '../customers/entities/customer-user.entity';
import { CustomerEntity } from '../customers/entities/customer.entity';
import { CreateGroupDto } from '../group/dto/create-group.dto';
import { IpGroupEntity } from '../group/entities/group.entity';
import { GroupEntity } from '../permissions/entities/permission-groups.entity';
import { UsersCustomersHasIpGroupsEntity } from '../customers/entities/users-customers-has-ip-groups.entity';
import { MailService } from '../mail/mail.service';
import { ChangeEmailPasswordDto } from './dtos/change-email-password.dto';
import { CustomerDto } from '../customers/dto/customer.dto';
import { fnAllowedCustomersFiltered } from '@/common/utils';
import {
  getPage,
  getPageSize,
  getTotalPage,
} from '@/common/utils/pagination.utils';
import { JwtService } from '@nestjs/jwt';
import { PartnerSettingsService } from '../partner-settings/partner-settings.service';
import { UsersPermissionGroupsEntity } from '../permissions/entities/users_permission_groups.entity';
import { PermissionsEntity } from '../permissions/entities/permissions.entity';

@Injectable()
export class UsersService {
  private readonly logger = new Logger(UsersService.name);
  constructor(
    @InjectEntityManager() private readonly manager: EntityManager,
    private readonly createCustomerService: CustomersService,
    private readonly mailService: MailService,
    private readonly partnerSettingsService: PartnerSettingsService,
    private readonly jwtService: JwtService,
  ) {}

  hashPassword(password: string) {
    const hashedPassword = hash(password, 10);
    return hashedPassword;
  }

  toUserDto(user: UserEntity): Partial<UserDto> {
    return {
      id: user.id,
      name: user.name,
      userName: user.userName,
      surname: user.surname,
      email: user.email,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      address: user.address,
      country: user.country,
      state: user.state,
      lastLoginAt: user.lastLoginAt,
      isSuspended: user.isSuspended,
      phoneNumber: user.phoneNumber,
      permissionGroups: user.permissionGroups,
      isTwoFactorAuthenticationEnabled: user.isTwoFactorAuthenticationEnabled,
      customers: user.customers,
      mustChangePassword: user.mustChangePassword,
      isMaster: user.isMaster,
    };
  }

  async createUser(
    user: Partial<UserDto>,
    req: IRequestWithUser,
  ): Promise<InsertResult | { message: string }> {
    return await this.manager.transaction(async entityManager => {
      this.logger.log('Creating user');
      const settings = await this.partnerSettingsService.getSecuritySettings(
        user.customers,
      );

      if (user.password.length < settings.minPasswordLength) {
        throw new HttpException(
          `Senha deve ter no mínimo ${settings.minPasswordLength} caracteres.`,
          HttpStatus.BAD_REQUEST,
        );
      }

      const regex = new RegExp(settings.passwordRegex);
      if (!regex.test(user.password)) {
        throw new HttpException(
          'A senha não atende aos critérios de complexidade.',
          HttpStatus.BAD_REQUEST,
        );
      }
      const hashedPassword = await this.hashPassword(user.password);
      const [existingUser, error] = await to<UserDto, Error>(
        entityManager.findOne(UserEntity, {
          relations: {
            customers: {
              users: true,
            },
          },
          withDeleted: false,
          where: {
            email: user.email,
            customers: {
              users: {
                user: {
                  email: user.email,
                },
              },
            },
          },
        }),
      );
      let hasCustomer;
      if (existingUser) {
        user.customers.map(customer => {
          hasCustomer = existingUser.customers.find(
            existingCustomer => existingCustomer.id === customer.id,
          );
          if (!hasCustomer) {
            this.updateUser(
              { customers: [customer] },
              existingUser.id,
              req.headers['client-ip'],
              req,
            );
            return 'ok';
          }
          throw new HttpException('Usuário já cadastrado', HttpStatus.CONFLICT);
        });
      }
      if (!hasCustomer && existingUser)
        return { message: 'User already exists, added to customer' };
      const userCreate = this.manager.create(UserEntity, {
        ...user,
        createdAt: new Date(),
        password: hashedPassword,
      });
      const [insertResult, err] = await to<InsertResult, Error>(
        entityManager.insert(UserEntity, userCreate),
      );

      await this.createCustomerService.createCustomerUser(
        { customers: user.customers },
        req,
        userCreate,
      );
      if (error || err)
        throw new HttpException(
          error.message || err.message,
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      return insertResult;
    });
  }

  async findOneUser(email: string): Promise<UserDto | undefined> {
    const [user, error] = await to<UserEntity[], Error>(
      this.manager.find(UserEntity, {
        relations: {
          customers: {
            users: {
              usersCustomersHasIpGroups: {
                ipGroup: {
                  ips: true,
                },
              },
            },
          },
          permissionGroups: {
            permissions: true,
          },
        },
        where: {
          email,
          customers: {
            users: {
              user: { email },
            },
          },
        },
      }),
    );
    if (error)
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);

    if (user.length) {
      const userSelected = user[0];
      if (userSelected.isMaster) {
        const partners = await this.manager.find(CustomerEntity, {
          relations: {
            ipGroups: true,
          },
        });
        const groups = await this.manager.find(GroupEntity, {
          relations: {
            permissions: true,
          },
        });
        userSelected.customers = partners;
        userSelected.permissionGroups = groups;
      }
      return userSelected;
    }
    return user[0];
  }

  async syncUsers(
    id?: string,
  ): Promise<{ id: string; name: string; surname: string }[]> {
    const [users, error] = await to<
      { id: string; name: string; surname: string }[],
      Error
    >(
      this.manager.find(UserEntity, {
        select: {
          id: true,
          userName: true,
          name: true,
          surname: true,
          email: true,
        },
        where: id ? { id } : {},
      }),
    );

    if (error)
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);

    return users;
  }

  async findOneById(id: string): Promise<Partial<UserDto> | undefined> {
    // 1. Buscar o usuário com as relações necessárias
    try{
      const rawUser = await this.manager
        .createQueryBuilder(UserEntity, 'user')
        .select('user.id', 'id')
        .addSelect('user.name', 'name')
        .addSelect('user.surname', 'surname')
        .addSelect('user.userName', 'userName')
        .addSelect('user.email', 'email')
        .addSelect('user.createdAt', 'createdAt')
        .addSelect('user.updatedAt', 'updatedAt')
        .addSelect('user.address', 'address')
        .addSelect('user.country', 'country')
        .addSelect('user.state', 'state')
        .addSelect('user.phoneNumber', 'phoneNumber')
        .addSelect('user.lastLoginAt', 'lastLoginAt')
        .addSelect('user.isSuspended', 'isSuspended')
        .addSelect(
          'user.isTwoFactorAuthenticationEnabled',
          'isTwoFactorAuthenticationEnabled'
        )
        .addSelect('user.isMaster', 'isMaster')
        .addSelect('user.mustChangePassword', 'mustChangePassword')
        .where('user.id = :id', { id })
        .getRawMany();

        const rawPermissionGroups = await this.manager
          .createQueryBuilder(UsersPermissionGroupsEntity, 'userGroup')
          .select('group.id', 'groupId')
          .addSelect('group.name', 'groupName')
          .addSelect('group.customers_id', 'customer_id')
          .leftJoin(
            GroupEntity,
            'group',
            'group.id = userGroup.permission_groups_id'
          )
          .leftJoin(
            'permissions_permission_groups',
            'ppg',
            'ppg.permission_groups_id = group.id'
          )
          .leftJoinAndSelect(
            PermissionsEntity,
            'permission',
            'permission.id = ppg.permissions_id'
          )
          .where('userGroup.users_id = :id', { id })
          .getRawMany();

          const rawCustomers = await this.manager
            .createQueryBuilder(CustomerEntity, 'customer')
            .select('customer.id', 'customerId')
            .addSelect('customer.name', 'customerName')
            .addSelect('customer.id_partner', 'idPartner')
            .leftJoinAndSelect(
              IpGroupEntity,
              'ipGroup',
            )
            .getRawMany();

            const rawHasIpGroups = await this.manager
              .createQueryBuilder(UsersCustomersHasIpGroupsEntity, 'hasIpGroup')
              .select('hasIpGroup.id', 'id')
              .select('ipGroup.id', 'ipGroupId')
              .leftJoinAndSelect(IpGroupEntity, 'ipGroup', 'ipGroup.id = hasIpGroup.ip_groups_id')
              .leftJoin(CustomerUserEntity, 'customerUser', 'customerUser.id = hasIpGroup.users_customers_id')
              .where('customerUser.users_id = :id', { id })
              .getRawMany();

      if (!rawUser.length) {
        throw new HttpException('User not found', HttpStatus.NO_CONTENT);
      }

      const ipMap = [];

      for(const row of rawHasIpGroups){
        ipMap.push({
          ipGroup: {
            id: row.ipGroupId  
          },
        });
      }
      const groupsMap = new Map();

      for (const row of rawPermissionGroups) {
        const groupId = row.groupId;

        if (!groupsMap.has(groupId)) {
          groupsMap.set(groupId, {
            id: groupId,
            name: row.groupName,
            customers_id: row.customer_id,
            permissions: [],
          });
        }

        if (row.permission_id) {
          groupsMap.get(groupId).permissions.push({
            id: row.permission_id,
            categoryId: row.permission_id_category,
            subcategoryId: row.permission_id_subcategory,
            method: row.permission_method,
            code: row.permission_code,
            description: row.permission_description,
            endpoint: row.permission_endpoint,
            createdAt: row.permission_createdAt,
            updatedAt: row.permission_updatedAt,
            deletedAt: row.permission_deletedAt,
          });
        }
      }
      const customersMap = new Map();

      for (const row of rawCustomers) {
        const customerId = row.customerId;

        if (!customersMap.has(customerId)) {
          customersMap.set(customerId, {
            id: customerId,
            name: row.customerName,
            idPartner: row.idPartner,
            ipGroups: [],
            users: [
              {
                usersCustomersHasIpGroups: ipMap
              },
            ],
          });
        }

        if (row.ipGroup_id) {
          customersMap.get(customerId).ipGroups.push({
            id: row.ipGroup_id,
            name: row.ipGroup_name,
            isActivated: row.ipGroup_is_activated,
            createdAt: row.ipGroup_created_at,
            createdBy: row.ipGroup_created_by,
            updatedAt: row.ipGroup_updated_at,
            customersId: row.ipGroup_customers_id,
            deletedAt: row.ipGroup_deletedAt,
          });
        }
      }

      const groupedCustomers = Array.from(customersMap.values());
      const groupedPermissionGroups = Array.from(groupsMap.values());

      const user: Partial<UserDto> = {
        id: rawUser[0].id,
        name: rawUser[0].name,
        surname: rawUser[0].surname,
        userName: rawUser[0].userName,
        email: rawUser[0].email,
        createdAt: rawUser[0].createdAt,
        updatedAt: rawUser[0].updatedAt,
        address: rawUser[0].address,
        country: rawUser[0].country,
        state: rawUser[0].state,
        phoneNumber: rawUser[0].phoneNumber,
        lastLoginAt: rawUser[0].lastLoginAt,
        isSuspended: rawUser[0].isSuspended,
        isTwoFactorAuthenticationEnabled:
          rawUser[0].isTwoFactorAuthenticationEnabled,
        isMaster: rawUser[0].isMaster,
        mustChangePassword: rawUser[0].mustChangePassword,
        permissionGroups: groupedPermissionGroups,
        customers: groupedCustomers,
      };

    if (user.isMaster) {
      const partners = await this.manager.find(CustomerEntity, {
        relations: {
          users: true,
        },
      });

      const groups = await this.manager.find(GroupEntity, {
        relations: {
          permissions: true,
        },
      });

      user.customers = partners;
      user.permissionGroups = groups;
    }

    return user;
    }
    catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }

  }

  async deleteUser(userId: string) {
    const [, error] = await to<DeleteResult, Error>(
      this.manager.softDelete(UserEntity, { id: userId }),
    );
    if (error)
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    return 'The record has been successfully deleted.';
  }

  async findAll(
    filter: GenericFilter & UserFilter,
    userId: string,
    allowedCustomers: CustomerDto[],
  ): Promise<{
    data: UserDto[];
    totalItems: number;
    totalPages: number;
    currentPage: number;
    pageSize: number;
  }> {
    try {
      if (!filter.sortOrder) {
        filter.sortOrder = SortOrder.ASC;
      }
      const page = getPage(filter?.page);
      const pageSize = getPageSize(filter?.pageSize);
      const customers = filter?.customers ? filter.customers.split(',') : [''];
      const allowedCustomersFiltered = fnAllowedCustomersFiltered(
        customers,
        allowedCustomers,
      );

      const totalItems = await this.manager.count(UserEntity, {
        relations: {
          customers: {
            ipGroups: true,
          },
          permissionGroups: true,
        },
        where: {
          email: filter.email ? Like(`%${filter.email}%`) : filter.email,
          name: filter.name ? Like(`%${filter.name}%`) : filter.name,
          surname: filter.surname
            ? Like(`%${filter.surname}%`)
            : filter.surname,
          userName: filter.userName
            ? Like(`%${filter.userName}%`)
            : filter.userName,
          customers: {
            id: In(allowedCustomersFiltered),
          },
        },
      });
      const totalPages = getTotalPage(totalItems, pageSize);
      const [data, error] = await to<UserEntity[], Error>(
        this.manager.find(UserEntity, {
          select: {
            id: true,
            name: true,
            userName: true,
            surname: true,
            email: true,
            createdAt: true,
            updatedAt: true,
            address: true,
            country: false,
            state: true,
            lastLoginAt: true,
            isSuspended: true,
            phoneNumber: true,
            mustChangePassword: true,
            isTwoFactorAuthenticationEnabled: true,
          },
          skip: (page - 1) * pageSize,
          take: pageSize,
          where: {
            email: filter.email ? Like(`%${filter.email}%`) : filter.email,
            name: filter.name ? Like(`%${filter.name}%`) : filter.name,
            surname: filter.surname
              ? Like(`%${filter.surname}%`)
              : filter.surname,
            userName: filter.userName
              ? Like(`%${filter.userName}%`)
              : filter.userName,
            customers: {
              id: In(allowedCustomersFiltered),
            },
          },
          order: { name: filter.sortOrder },
        }),
      );

      if (error) {
        throw new HttpException(
          error.message,
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }

      return {
        data: data.map(user => ({ ...user, canDelete: user.id !== userId })),
        totalItems,
        totalPages,
        currentPage: page,
        pageSize,
      };
    } catch (error) {
      console.log('Error in findAll users service:', error);
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async setTwoFactorAuthenticationSecret(secret: string, id: string) {
    const [inserted, error] = await to<UserDto, Error>(
      this.manager.save(UserEntity, {
        id,
        twoFactorAuthenticationSecret: secret,
      }),
    );

    if (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    return inserted;
  }

  async turnOnTwoFactorAuthentication(id: string) {
    const [inserted, error] = await to<UserDto, Error>(
      this.manager.save(UserEntity, {
        id,
        isTwoFactorAuthenticationEnabled: true,
      }),
    );
    if (error)
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    return inserted;
  }

  async updateUser(
    updateUserDto: UpdateUserDto,
    id: string,
    ip: string,
    req: IRequestWithUser,
  ): Promise<InsertResult[]> {
    try {
      return await this.manager.transaction(async entityManager => {
        const userDidId = req.user.id;
        const [oldUser, error] = await to<UserEntity, Error>(
          entityManager.findOne(UserEntity, {
            relations: {
              customers: {
                users: true,
              },
            },
            withDeleted: false,
            where: {
              id,
              customers: {
                users: {
                  user: {
                    id,
                  },
                },
              },
            },
          }),
        );

        if (error) {
          throw new HttpException(
            error.message,
            HttpStatus.INTERNAL_SERVER_ERROR,
          );
        }

        if (updateUserDto.customers?.length > 0) {
          const existingCustomerIds = new Set(
            oldUser.customers.map(el => el.id),
          );
          const newCustomers = updateUserDto.customers.filter(
            customer => !existingCustomerIds.has(customer.id),
          );
          if (newCustomers.length > 0) {
            await this.createCustomerService.createCustomerUser(
              { customers: newCustomers },
              req,
              oldUser,
            );
          }
          delete updateUserDto.customers;
        }

        const [, err] = await to<UpdateResult, Error>(
          entityManager.update(UserEntity, { id }, updateUserDto),
        );

        if (err) {
          throw new HttpException(
            err.message,
            HttpStatus.INTERNAL_SERVER_ERROR,
          );
        }

        const newUserArr = Object.entries(updateUserDto);
        const timeStamp = new Date();
        const logs = newUserArr.map(async ([key, value]) => {
          const log = new LogEntity();
          log.ip = ip;
          log.field = key;
          log.oldValue = oldUser?.[key] || '';
          log.newValue = value ? value : '';
          log.user = oldUser;
          log.userDidId = userDidId;
          log.updatedAt = timeStamp;

          const [inserted, error] = await to<InsertResult, Error>(
            entityManager.insert(LogEntity, log),
          );

          if (error) {
            throw new HttpException(
              error.message,
              HttpStatus.INTERNAL_SERVER_ERROR,
            );
          }

          return inserted;
        });
        return await Promise.all(logs);
      });
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async resetTwoFactor(id: string, ip: string, req: IRequestWithUser) {
    try {
      return await this.manager.transaction(async (entityManager) => {
        const userDidId = req.user.id;
        const [oldUser, error] = await to<UserEntity, Error>(
          entityManager.findOne(UserEntity, {
            relations: {
              customers: {
                users: true,
              },
            },
            withDeleted: false,
            where: {
              id,
              customers: {
                users: {
                  user: {
                    id,
                  },
                },
              },
            },
          })
        );

        if (error) {
          throw new HttpException(
            error.message,
            HttpStatus.INTERNAL_SERVER_ERROR
          );
        }

        const [, err] = await to<UpdateResult, Error>(
          entityManager.update(UserEntity, { id }, { isTwoFactorAuthenticationEnabled: false, twoFactorAuthenticationSecret: null })
        );

        if (err) {
          throw new HttpException(
            err.message,
            HttpStatus.INTERNAL_SERVER_ERROR
          );
        }

        const newUserArr = Object.entries({ isTwoFactorAuthenticationEnabled: "false", twoFactorAuthenticationSecret: 'null' });
        const timeStamp = new Date();
        const logs = newUserArr.map(async ([key, value]) => {
          const log = new LogEntity();
          log.ip = ip;
          log.field = key;
          log.oldValue = oldUser?.[key] || '';
          log.newValue = value ? value : '';
          log.user = oldUser;
          log.userDidId = userDidId;
          log.updatedAt = timeStamp;

          const [inserted, error] = await to<InsertResult, Error>(
            entityManager.insert(LogEntity, log)
          );

          if (error) {
            throw new HttpException(
              error.message,
              HttpStatus.INTERNAL_SERVER_ERROR
            );
          }

          return inserted;
        });
        return await Promise.all(logs);
      });
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async changePassword(
    updateUserDto: UpdateUserDto,
    id: string,
    ip: string,
    userDidId: string,
  ): Promise<InsertResult[]> {
    return await this.manager.transaction(async entityManager => {
      const [oldUser, errorUserEntity] = await to<UserEntity, Error>(
        entityManager.findOneBy(UserEntity, { id }),
      );
      if (errorUserEntity) {
        console.log('>>>>>> UserEntity error:', errorUserEntity.message);
        throw new HttpException(
          'Erro ao tentar atualizar senha do usuário. [code: 1]',
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }

      const hashPassword = await this.hashPassword(updateUserDto.password);
      const isMatch = await compare(updateUserDto.password, oldUser.password);
      if (isMatch) {
        throw new Error(
          'É necessário que a nova senha seja diferente da anterior. [code: 3]',
        );
      }

      const timeStamp = new Date();
      updateUserDto.password = hashPassword;
      updateUserDto.lastPasswordUpdate = timeStamp;
      const [, errorUpdateResult] = await to<UpdateResult, Error>(
        entityManager.update(UserEntity, { id }, updateUserDto),
      );
      if (errorUpdateResult) {
        console.log('>>>>>> UpdateResult error:', errorUpdateResult.message);
        throw new HttpException(
          'Erro ao tentar atualizar senha do usuário. [code: 2]',
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }

      const newUserArr = Object.entries(updateUserDto);
      const logs = newUserArr.map(async ([key, value]) => {
        const log = new LogEntity();
        log.ip = ip;
        log.field = key;
        log.oldValue = oldUser[key] || '';
        log.newValue = value;
        log.user = oldUser;
        log.userDidId = userDidId;
        log.updatedAt = timeStamp;

        const [inserted, error] = await to<InsertResult, Error>(
          entityManager.insert(LogEntity, log),
        );
        if (error) {
          console.log('>>>>>> LogEntity error:', error.message);
          throw new HttpException(
            'Erro ao tentar salvar o log. [code: 4]',
            HttpStatus.INTERNAL_SERVER_ERROR,
          );
        }
        return inserted;
      });
      return await Promise.all(logs);
    });
  }

  async changePasswordMySelf(
    id: string,
    ip: string,
    user: UserDto,
    body: ChangePasswordDto,
  ): Promise<InsertResult[]> {
    const [userEntity, userError] = await to<UserEntity, Error>(
      this.manager.findOneBy(UserEntity, { id }),
    );
    if (userError)
      throw new HttpException(
        userError.message,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    return await this.manager.transaction(async entityManager => {
      const hashPassword = await this.hashPassword(body.password);

      const isMatch = await compare(body.password, user.password);

      const passwordIsValid = await compare(
        body.currentPassword,
        user.password,
      );

      if (!passwordIsValid) throw new Error('Senhas não coincidem.');
      if (isMatch)
        throw new Error('Senha nova deve ser diferente da antiga.');

      body.password = hashPassword;

      const timeStamp = new Date();

      const [, err] = await to<UpdateResult, Error>(
        entityManager.update(
          UserEntity,
          { id },
          { password: body.password, lastPasswordUpdate: timeStamp },
        ),
      );

      if (err)
        throw new HttpException(err.message, HttpStatus.INTERNAL_SERVER_ERROR);

      const newUserArr = Object.entries(body);
      const logs = newUserArr.map(async ([key, value]) => {
        const log = new LogEntity();
        log.ip = ip;
        log.field = key;
        log.oldValue = user[key];
        log.newValue = value;
        log.user = userEntity;
        log.userDidId = user.id;
        log.updatedAt = timeStamp;

        const [inserted, error] = await to<InsertResult, Error>(
          entityManager.insert(LogEntity, log),
        );
        if (error)
          throw new HttpException(
            error.message,
            HttpStatus.INTERNAL_SERVER_ERROR,
          );
        return inserted;
      });
      return await Promise.all(logs);
    });
  }

  async changePasswordFirstAccess(
    id: string,
    ip: string,
    user: Partial<UserDto>,
    body: ChangePasswordDto,
  ): Promise<InsertResult[]> {
    const [userEntity, userError] = await to<UserEntity, Error>(
      this.manager.findOneBy(UserEntity, { id }),
    );
    if (userError)
      throw new HttpException(
        userError.message,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    return await this.manager.transaction(async entityManager => {
      const hashPassword = await this.hashPassword(body.password);

      const isMatch = await compare(body.password, user.password);

      if (isMatch)
        throw new HttpException(
          'Senha nova deve ser diferente da antiga.',
          HttpStatus.BAD_REQUEST,
        );

      body.password = hashPassword;

      const [, err] = await to<UpdateResult, Error>(
        entityManager.update(
          UserEntity,
          { id },
          {
            password: body.password,
            resetCode: null,
            resetExpires: null,
            isResetCode: false,
            mustChangePassword: false,
          },
        ),
      );

      if (err)
        throw new HttpException(err.message, HttpStatus.INTERNAL_SERVER_ERROR);

      const newUserArr = Object.entries(body);
      const timeStamp = new Date();
      const logs = newUserArr.map(async ([key, value]) => {
        const log = new LogEntity();
        log.ip = ip;
        log.field = key;
        log.oldValue = user[key];
        log.newValue = value;
        log.user = userEntity;
        log.userDidId = user.id;
        log.updatedAt = timeStamp;

        const [inserted, error] = await to<InsertResult, Error>(
          entityManager.insert(LogEntity, log),
        );
        if (error)
          throw new HttpException(
            error.message,
            HttpStatus.INTERNAL_SERVER_ERROR,
          );
        return inserted;
      });
      return await Promise.all(logs);
    });
  }

  async updateIp(groupsIps: Partial<CreateGroupDto[]>, id: string) {
    const [user, userError] = await to<UserEntity, Error>(
      this.manager.findOneBy(UserEntity, { id }),
    );
    if (userError || !user) {
      throw new HttpException(
        userError?.message || 'Usuário não encontrado',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }

    const [customers, customersError] = await to<CustomerEntity, Error>(
      this.manager.findOne(CustomerEntity, {
        where: {
          users: {
            user: { id },
          },
        },
      }),
    );

    if (customersError || !customers) {
      throw new HttpException(
        customersError?.message || 'Customer Not Found',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
    const customerUser = await this.manager.findOne(CustomerUserEntity, {
      where: {
        user: { id: user.id },
        customers: { id: customers.id },
      },
      relations: {
        customers: true,
        usersCustomersHasIpGroups: {
          ipGroup: true,
        },
      },
    });

    const notAlter = customerUser.usersCustomersHasIpGroups.filter(c=> groupsIps.some(g=>g.id == c.ipGroup.id));
    const newGroups = customerUser.usersCustomersHasIpGroups.filter(c=> groupsIps.find(g=>g.id != c.ipGroup.id));


    for (let i = 0; i < groupsIps.length; i++) {
      if (!customerUser.usersCustomersHasIpGroups)
        throw new HttpException('User not found', HttpStatus.NOT_FOUND);
      const hasIpGroup = customerUser.usersCustomersHasIpGroups.some(
        group => group.ipGroup.id === groupsIps[i].id,
      );
      if (hasIpGroup) {
        throw new HttpException(
          'Usuário já possui este grupo',
          HttpStatus.CONFLICT
        );
      }
      const [ipGroup, ipGroupError] = await to<IpGroupEntity, Error>(
        this.manager.findOneBy(IpGroupEntity, { id: groupsIps[i].id }),
      );

      if (ipGroupError || !ipGroup) {
        throw new HttpException(
          ipGroupError?.message || 'Group Not Found',
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }

      const createdCustomerUser = this.manager.create(
        UsersCustomersHasIpGroupsEntity,
        {
          customerUser: customerUser,
          ipGroup: ipGroup,
        },
      );
      const [, saveError] = await to(
        this.manager.insert(
          UsersCustomersHasIpGroupsEntity,
          createdCustomerUser,
        ),
      );

      if (saveError) {
        throw new HttpException(
          saveError.message,
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }
    }
  }

  async changePasswordEmail(body: ChangeEmailPasswordDto, ip) {
    const { resetCode, newPassword, confirmNewPassword } = body;

    if (newPassword !== confirmNewPassword) {
      throw new HttpException('Passwords do not match', HttpStatus.BAD_REQUEST);
    }

    const [user, error] = await to<UserEntity, Error>(
      this.manager.findOne(UserEntity, { where: { resetCode } }),
    );

    if (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    const isValid = user.resetCode === resetCode;

    if (!isValid) {
      throw new HttpException(`Invalid reset code`, HttpStatus.BAD_REQUEST);
    }

    const isTime = user.resetExpires
      ? user.resetExpires?.getTime() - new Date().getTime()
      : 0;
    if (isTime < 1) {
      throw new HttpException('Expired reset code', HttpStatus.BAD_REQUEST);
    }

    const hashPassword = await this.hashPassword(newPassword);
    const isMatch = await compare(newPassword, user.password);

    if (isMatch) {
      throw new HttpException(
        'Senha nova deve ser diferente da antiga.',
        HttpStatus.BAD_REQUEST,
      );
    }

    await this.manager.transaction(async entityManager => {
      const [, err] = await to<UpdateResult, Error>(
        entityManager.update(
          UserEntity,
          { id: user.id },
          {
            password: hashPassword,
            resetCode: null,
            resetExpires: null,
            isResetCode: false,
            mustChangePassword: false,
          },
        ),
      );

      if (err) {
        throw new HttpException(err.message, HttpStatus.INTERNAL_SERVER_ERROR);
      }

      const newUserArr = Object.entries(body);
      const timeStamp = new Date();
      const logs = newUserArr.map(async ([key, value]) => {
        const log = new LogEntity();
        log.ip = ip;
        log.field = key;
        log.oldValue = user[key] || '';
        log.newValue = value;
        log.user = user;
        log.userDidId = user.id;
        log.updatedAt = timeStamp;

        const [inserted, error] = await to<InsertResult, Error>(
          entityManager.insert(LogEntity, log),
        );

        if (error) {
          throw new HttpException(
            error.message,
            HttpStatus.INTERNAL_SERVER_ERROR,
          );
        }

        return inserted;
      });
      return await Promise.all(logs);
    });

    return { message: 'Password changed successfully', statusCode: 200 };
  }

  async resetPassword(email: string, req: Request) {
    const code = Math.floor(Math.random() * (999999 - 100000 + 1)) + 100000;
    const resetTime = new Date();
    const data = {
      to: email,
      text: `Código para reset de senha no email: ${code}`,
      subject: 'Título do E-mail',
    };
    const [user, error] = await to<UserEntity, Error>(
      this.manager.findOne(UserEntity, { where: { email: email } }),
    );
    if (error || !user || user.email !== email)
      throw new HttpException(
        error ? error.message : 'User not found',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );

    await this.manager.update(UserEntity, user.id, {
      resetCode: code.toString(),
      resetExpires: new Date(resetTime.setHours(resetTime.getHours() + 1)),
      isResetCode: true,
      mustChangePassword: true,
    });
    const messageResponse = await this.mailService.sendEmail(data);

    const payload = {
      email: user.email,
      isTwoFactorAuthenticationEnabled: user.isTwoFactorAuthenticationEnabled,
    };

    const access_token = this.jwtService.sign(payload, {
      secret: process.env.JWT_SECRET,
      expiresIn: '1h',
    });

    return {
      email: payload.email,
      access_token,
      mustChangePassword: user.mustChangePassword,
      isResetCode: user.isResetCode,
      message: messageResponse.data,
    };
  }

  async confirmEmail(email: string) {
    const code = Math.floor(Math.random() * (999999 - 100000 + 1)) + 100000;
    const data = {
      to: email,
      text: `Código confirmar email: ${code}`,
      subject: 'Título do E-mail',
    };
    const [user, error] = await to<UserEntity, Error>(
      this.manager.findOne(UserEntity, { where: { email: email } }),
    );
    if (error || !user || user.email !== email)
      throw new HttpException(
        error ? error.message : 'User not found',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    const [, err] = await to<UpdateResult, Error>(
      this.manager.update(UserEntity, user.id, {
        codeEmailConfirmed: code,
      }),
    );
    if (err)
      throw new HttpException(err.message, HttpStatus.INTERNAL_SERVER_ERROR);
    const messageResponse = await this.mailService.sendEmail(data);
    return messageResponse.data;
  }

  async turnOnEmailValidation(email: string, code: number) {
    const [user, error] = await to<UserEntity, Error>(
      this.manager.findOne(UserEntity, { where: { email: email } }),
    );
    if (error || !user || user.email !== email)
      throw new HttpException(
        error ? error.message : 'User not found',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    const isValid = user.codeEmailConfirmed === Number(code);
    if (!isValid)
      throw new HttpException('Invalid code', HttpStatus.BAD_REQUEST);
    this.manager.update(UserEntity, user.id, {
      isEmailConfirmed: true,
    });
    return 'Email confirmed successfully';
  }

  async checkIp(partnerId: string, userId: string, ipClient: string) {
    return true; // add to give full acccess for everyone
    const user = await this.findOneById(userId);
    if (user.isMaster) {
      return true;
    }

    const userCustomer = user.customers.find(el => el.id === partnerId);
    const groups = userCustomer?.users || [];
    let ip = ipClient;
    if (ipClient.includes(',')) {
      ip = ipClient.split(',')[0];
    }

    if (groups.length > 0) {
      const group = groups[0].usersCustomersHasIpGroups?.filter(
        el => el.ipGroup.ips.filter(subEl => subEl.ip === ip).length > 0,
      );

      return !!group[0]?.ipGroup.ips[0].ip;
    }

    return false;
  }

  async checkPermissions(
    userId: string,
    path: string,
    method: string,
    customerPartnerId?: string,
  ): Promise<boolean> {
    const user = await this.manager
      .createQueryBuilder(UserEntity, 'user')
      .select('user.isGod', 'isMaster')
      .leftJoinAndSelect('user.customers', 'customers')
      .where('user.id = :userId', { userId })
      .groupBy('customers.id')
      .getRawMany();
    if (user[0].isMaster) {
      return true;
    }
    const permissions = await this.manager
      .createQueryBuilder(UsersPermissionGroupsEntity, 'userPermissionGroups')
      .leftJoinAndSelect(
        'permissions_permission_groups',
        'permissions',
        'permissions.permission_groups_id = userPermissionGroups.permission_groups_id',
      )
      .leftJoinAndSelect(
        PermissionsEntity,
        'permission',
        'permission.id = permissions.permissions_id',
      )
      .leftJoinAndSelect(
        GroupEntity,
        'groups',
        'groups.id = userPermissionGroups.permission_groups_id',
      )
      .where('userPermissionGroups.users_id = :userId', { userId })
      .andWhere('groups.customers_id = :customerPartnerId', {
        customerPartnerId,
      })
      .andWhere('permission.endpoint = :path', { path })
      .andWhere('permission.method = :method', { method })
      .getRawMany();

    console.log(permissions.length);
    if (!permissions?.length) return false;

    return !!permissions;
  }

  async findDidEdit() {
    const logUserDid = await this.manager
      .createQueryBuilder(LogEntity, 'log')
      .select('DISTINCT log.userDidId', 'userDidId')
      .where('log.userDidId IS NOT NULL')
      .getRawMany();

    const user = [];
    for (let i = 0; i < logUserDid.length; i++) {
      const userId = logUserDid[i].userDidId;
      const [users, error] = await to<UserEntity, Error>(
        this.manager.findOne(UserEntity, {
          select: {
            id: true,
            name: true,
            userName: true,
          },
          where: {
            id: userId,
          },
        }),
      );
      user.push(users);
      if (error)
        throw new HttpException(
          error.message,
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
    }
    const filterUser = user.filter(el => el !== null);
    return filterUser;
  }
}
