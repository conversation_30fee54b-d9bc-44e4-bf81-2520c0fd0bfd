import { HttpService } from "@nestjs/axios";
import { Injectable } from "@nestjs/common";
import { firstValueFrom } from "rxjs";

@Injectable()
export class MailService {
    constructor( 
        private readonly httpSerivce:HttpService,
    ) {}

    private apiMail = process.env.API_MAIL
    async sendEmail(data) {
        const response  = await firstValueFrom(this.httpSerivce.post(`${this.apiMail}/email/send`, data));
        return response
    }
}