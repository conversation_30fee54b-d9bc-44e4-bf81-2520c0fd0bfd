import { Module } from "@nestjs/common";
import { AuthenticationModule } from "@/modules/authentication/authentication.module";
import { UsersModule } from "@/modules/users/users.module";
import { LogsModule } from "@/modules/logHistory/logs.module";
import { PermissionsModule } from "@/modules/permissions/permissions.module";
import { IPModule } from "@/modules/ips/ip.module";
import { IPGroupsModule } from "@/modules/group/ip-groups.module";
import "dotenv/config";
import { CustomersModule } from "./modules/customers/customers.module";
import { HealthModule } from "./health/health.module";
import { UserSessionsModule } from "./modules/user-sessions/user-sessions.module";
import { UsersLoginHistoricModule } from "./modules/users-login-historic/users-login-historic.module";
import { PartnerSettingsModule } from "./modules/partner-settings/partner-settings.module";

@Module({
  imports: [
    CustomersModule,
    AuthenticationModule,
    UsersModule,
    LogsModule,
    PermissionsModule,
    IPModule,
    IPGroupsModule,
    HealthModule,
    UserSessionsModule,
    UsersLoginHistoricModule,
    PartnerSettingsModule,
  ],
})
export class AppModule {}
