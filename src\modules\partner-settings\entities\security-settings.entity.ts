import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedC<PERSON>umn,
  <PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
} from "typeorm";

@Entity({ name: "partner_security_settings", schema: "partners" })
export class PartnerSecuritySettings {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ name: "id_partner", type: "uuid" })
  idPartner: string;

  @Column({ name: "password_expiration_days", type: "int" })
  passwordExpirationDays: number;

  @Column({ name: "temporary_password_expiration_days", type: "int" })
  temporaryPasswordExpirationDays: number;

  @Column({ name: "min_password_length", type: "int" })
  minPasswordLength: number;

  @Column({ name: "password_regex", type: "text" })
  passwordRegex: string;

  @Column({ name: "password_attempt_limit", type: "int" })
  passwordAttemptLimit: number;

  @Column({ name: "password_expiration_notice_days", type: "int" })
  passwordExpirationNoticeDays: number;

  @Column({ name: "login_block_minutes", type: "int" })
  loginBlockMinutes: number;

  @CreateDateColumn({ name: "created_at", type: "timestamptz" })
  createdAt: Date;

  @UpdateDateColumn({ name: "updated_at", type: "timestamptz" })
  updatedAt: Date;

  @DeleteDateColumn({ name: "deleted_at", type: "timestamptz" })
  deletedAt?: Date;
}
