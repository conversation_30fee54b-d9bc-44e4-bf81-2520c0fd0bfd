import { forwardRef, Module } from '@nestjs/common';
import { UserSessionsService } from './user-sessions.service';
import { DatabaseModule } from '@/common/database/users/database.module';
import { UserSessionController } from './user-session.controller';
import { AuthenticationModule } from '../authentication/authentication.module';
import { UsersModule } from '../users/users.module';
import { PermissionsService } from '../permissions/permissions.service';

@Module({
  imports: [
    DatabaseModule,
    forwardRef(() => AuthenticationModule),
    UsersModule,
  ],
  controllers: [UserSessionController],
  providers: [UserSessionsService, PermissionsService],
  exports: [UserSessionsService]
})

export class UserSessionsModule { }
