import {
  HttpException,
  HttpStatus,
  Injectable,
  Logger,
  UnauthorizedException,
} from '@nestjs/common';
import { UsersService } from '@/modules/users/users.service';
import { User } from '@/modules/users/interface/user.interface';
import { JwtService } from '@nestjs/jwt';
import { authenticator } from 'otplib';
import { toDataURL } from 'qrcode';
import { compare } from 'bcrypt';
import { to } from '@/common/utils/to';
import { UserDto } from '@/modules/users/dtos/user.dto';
import { UserSessionsService } from '../user-sessions/user-sessions.service';
import { PartnerSettingsService } from '../partner-settings/partner-settings.service';
import * as dayjs from 'dayjs';
import { MailService } from '../mail/mail.service';
import { UsersLoginHistoricService } from '../users-login-historic/users-login-historic.service';
import { SecuritySettingsDto } from '../partner-settings/dto/security-settings.dto';
import { PartnerSecuritySettings } from '../partner-settings/entities/security-settings.entity';
import { removeKeysFromObject } from '@/common/utils';
import { CustomerEntity } from '../customers/entities/customer.entity';
import { IRequestWithUser } from '../users/interface/request-user.interface';
import { MicrosoftSSODto } from './dtos/microsoft-sso.dto';
import { EntityManager } from 'typeorm';
import { LogEntity } from '../logHistory/entities/log.entity';
import { UserEntity } from '../users/entities/user.entity';

interface ValidatedUser extends Partial<User> {
  settings: SecuritySettingsDto | PartnerSecuritySettings;
}

@Injectable()
export class AuthenticationService {
  private readonly logger = new Logger(AuthenticationService.name);

  constructor(
    private usersService: UsersService,
    private jwtService: JwtService,
    private userSessionService: UserSessionsService,
    private partnerSettingsService: PartnerSettingsService,
    private mailService: MailService,
    private usersLoginHistoricService: UsersLoginHistoricService,
    private manager: EntityManager,
  ) { }

  async validateUser(
    email: string,
    pass: string,
    ip: string,
    device: string,
  ): Promise<ValidatedUser> {
    const [user, error] = await to<UserDto, Error>(
      this.usersService.findOneUser(email),
    );

    if (error) {
      await this.usersLoginHistoricService.create(
        null,
        email,
        false,
        ip,
        device,
      );
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    if (!user) {
      await this.usersLoginHistoricService.create(
        null,
        email,
        false,
        ip,
        device,
      );
      throw new HttpException('User not found', HttpStatus.NOT_FOUND);
    }

    const userWithoutPassword = removeKeysFromObject(user, ['password']);

    const settings = await this.partnerSettingsService.getSecuritySettings(
      user.customers,
    );

    // const isBlocked = await this.usersLoginHistoricService.isBlocked(
    //   email,
    //   user.id,
    //   settings.passwordAttemptLimit,
    //   settings.loginBlockMinutes,
    // );

    // if (isBlocked) {
    //   throw new UnauthorizedException(
    //     'Usuário temporariamente bloqueado por tentativas inválidas',
    //   );
    // }

    if (user.isResetCode) {
      return { ...userWithoutPassword, settings };
    }

    const [isMatch, err] = await to<boolean, Error>(
      compare(pass, user.password),
    );

    if (err) {
      await this.usersLoginHistoricService.create(
        user.id,
        email,
        false,
        ip,
        device,
      );
      throw new HttpException(err.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    // if (user.isSuspended) {
    //   await this.usersLoginHistoricService.create(
    //     user.id,
    //     email,
    //     false,
    //     ip,
    //     device
    //   );
    //   throw new HttpException("User is suspended", HttpStatus.FORBIDDEN);
    // }

    await this.usersLoginHistoricService.create(
      user.id,
      email,
      isMatch,
      ip,
      device,
    );

    // if (user && isMatch &&  user.isEmailConfirmed ) {
    if (user && isMatch) {
      return { ...userWithoutPassword, settings };
    }
  }

  validateToken(bearer: string): Partial<User | null> {
    const token = bearer.replace('Bearer ', '');
    try {
      if (this.jwtService.verify(token, { secret: process.env.JWT_SECRET })) {
        return this.jwtService.decode(token);
      }
    } catch (error) {
      console.log(
        '- - - - - - - - - - - LOG DE ERRO NA AUTENTICAÇÃO - - - - - - - - - - - ',
      );
      console.log(error);
      throw new HttpException(error, HttpStatus.UNAUTHORIZED);
    }
  }

  async login(
    userWithoutPsw: Partial<User>,
    req: Request,
    settings: SecuritySettingsDto,
  ) {
    const ip = req.headers['client-ip'];
    const device = req.headers['user-agent'];

    const user = await this.usersService.findOneUser(userWithoutPsw.email);

    this.logger.log(`Login solicitado pelo usuário ${userWithoutPsw.email}`);
    this.logger.debug(
      `Parceiros vinculados: ${JSON.stringify(userWithoutPsw.customers)}`,
    );

    this.logger.debug(`Configuração aplicada: ${JSON.stringify(settings)}`);

    const passwordAge = dayjs().diff(userWithoutPsw.lastPasswordUpdate, 'day');

    this.logger.debug(
      `Senha do usuário ${userWithoutPsw.email} foi atualizada há ${passwordAge} dia(s)`,
    );

    if (passwordAge >= settings.passwordExpirationDays) {
      throw new UnauthorizedException('Senha expirada, altere sua senha');
    }

    this.handlePasswordExpiryNotice(userWithoutPsw, settings, passwordAge);

    const sessionId = await this.userSessionService.createUserSession({
      userId: userWithoutPsw.id,
      ip,
      device,
    });

    const payload = {
      email: userWithoutPsw.email,
      isTwoFactorAuthenticationEnabled:
        userWithoutPsw.isTwoFactorAuthenticationEnabled,
      sessionId,
    };

    const access_token = this.jwtService.sign(payload, {
      secret: process.env.JWT_SECRET,
      expiresIn: '1d',
    });

    return {
      email: payload.email,
      access_token,
      mustChangePassword: user.mustChangePassword,
      isResetCode: user.isResetCode,
    };
  }

  async loginWith2fa(userWithoutPsw: Partial<User>, request: any) {
    const { customers, ...userWithoutCustomersUser } = userWithoutPsw;
    const ip = request.headers['client-ip'];

    const allowedCustomers = [];
    const ipGroups = [];

    const ipRestrictionsList =
      await this.partnerSettingsService.getIpRestrictionsForPartners(customers);

    for (const customer of customers) {
      const isBlocked = ipRestrictionsList.some(
        r => r.idPartner === customer.id && r.ipAddress.split('/')[0] === ip,
      );

      if (!isBlocked) {
        allowedCustomers.push(customer);

        if (!userWithoutCustomersUser.isMaster) {
          customer.users?.forEach(user => {
            ipGroups.push(user.usersCustomersHasIpGroups);
            delete user.usersCustomersHasIpGroups;
          });
          delete customer.users;
        }
      }
    }

    const payload = {
      email: userWithoutCustomersUser.email,
      name: userWithoutCustomersUser.name,
      isTwoFactorAuthenticationEnabled:
        !!userWithoutCustomersUser.isTwoFactorAuthenticationEnabled,
      isTwoFactorAuthenticated: true,
      userId: userWithoutCustomersUser.id,
      customers: allowedCustomers,
      isDev: userWithoutCustomersUser.isDev,
    };

    const access_token = this.jwtService.sign(payload, {
      secret: process.env.JWT_SECRET,
      expiresIn: '1d',
    });

    return {
      email: payload.email,
      name: userWithoutCustomersUser.name,
      mustChangePassword: userWithoutCustomersUser.mustChangePassword,
      access_token,
      permissionGroups: userWithoutPsw.permissionGroups || [],
      ipGroups: ipGroups[0] || [],
    };
  }

  async generateTwoFactorAuthenticationSecret(user: User, isPam: boolean) {
    const secret = authenticator.generateSecret();
    const tagName = isPam ? 'H2_PAM' : 'H2_COMPLIANCE';
    const otpAuthUrl = authenticator.keyuri(user.email, tagName, secret);
    await this.usersService.setTwoFactorAuthenticationSecret(secret, user.id);

    return {
      secret,
      otpAuthUrl,
    };
  }

  async generateQrCodeDataURL(otpAuthUrl: string) {
    return toDataURL(otpAuthUrl);
  }

  isTwoFactorAuthenticationCodeValid(
    twoFactorAuthenticationCode: string,
    user: User,
  ) {
    return authenticator.verify({
      token: twoFactorAuthenticationCode,
      secret: user.twoFactorAuthenticationSecret,
    });
  }

  private async handlePasswordExpiryNotice(
    user: Partial<User>,
    settings,
    passwordAge: number,
  ): Promise<void> {
    const { passwordExpirationDays, passwordExpirationNoticeDays } = settings;

    if (
      passwordExpirationDays &&
      passwordExpirationNoticeDays &&
      passwordAge >= passwordExpirationDays - passwordExpirationNoticeDays
    ) {
      const daysRemaining = passwordExpirationDays - passwordAge;

      this.logger.log(
        `Aviso: a senha do usuário ${user.email} expira em ${daysRemaining} dia(s)`,
      );

      const mail = {
        to: user.email,
        subject: 'Sua senha está prestes a expirar',
        text: `Olá ${user.name},\n\nSua senha irá expirar em ${daysRemaining} dia(s).\nPor favor, acesse seu perfil e altere a senha para continuar acessando o sistema normalmente.\n\nEquipe de Suporte`,
      };

      this.mailService.sendEmail(mail);
    }
  }

  async validateMicrosoftToken(
    microsoftTokenData: MicrosoftSSODto,
    req: any
  ) {
    try {
      const decodedToken = this.jwtService.decode(microsoftTokenData.microsoftToken) as any;

      if (!decodedToken) {
        throw new UnauthorizedException('Token da Microsoft inválido');
      }

      const email = decodedToken.email ||
        decodedToken.upn ||
        decodedToken.unique_name ||
        decodedToken.preferred_username;

      this.logger.log(`Tentativa de login SSO Microsoft pelo usuário ${email}`);

      let user = await this.usersService.findOneUser(email);

      if (!user) {
        this.logger.log(`Usuário ${email} não encontrado. Iniciando auto-cadastro via SSO Microsoft.`);

        const name = decodedToken.given_name ||
          decodedToken.name?.split(' ')[0] ||
          email.split('@')[0];

        const surname = decodedToken.family_name ||
          decodedToken.surname ||
          (decodedToken.name?.split(' ').slice(1).join(' ')) ||
          '';

        user = await this.createSSOUser(email, name, surname, req);
      }

      const settings = await this.partnerSettingsService.getSecuritySettings(user.customers);

      this.handlePasswordExpiryNotice(user, settings, 0);

      const ip = req.headers['client-ip'];
      const device = req.headers['user-agent'];
      await this.usersLoginHistoricService.create(user.id, email, true, ip, device, true);

      const sessionId = await this.userSessionService.createUserSession({
        userId: user.id,
        ip,
        device,
      });

      const payload = {
        email: user.email,
        isTwoFactorAuthenticationEnabled: user.isTwoFactorAuthenticationEnabled,
        sessionId,
      };

      const access_token = this.jwtService.sign(payload, {
        secret: process.env.JWT_SECRET,
        expiresIn: '1d',
      });

      this.logger.log(`Login SSO bem-sucedido para ${email}.`);

      return {
        email: payload.email,
        access_token,
        mustChangePassword: user.mustChangePassword || false,
        isResetCode: user.isResetCode || false,
      };

    } catch (error) {
      this.logger.error(`Erro na autenticação Microsoft SSO: ${error.message}`);
      throw new UnauthorizedException('Falha na autenticação Microsoft');
    }
  }

  /**
 * Gera uma senha temporária segura que atende aos critérios de complexidade
 */
  private generateSecureTemporaryPassword(): string {
    const lowercase = 'abcdefghijklmnopqrstuvwxyz';
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const numbers = '0123456789';
    const symbols = '$&+,:;=?@#|\'<>.^*()%!-';

    let password = '';

    // garante pelo menos 1 de cada tipo obrigatório
    password += lowercase[Math.floor(Math.random() * lowercase.length)];
    password += uppercase[Math.floor(Math.random() * uppercase.length)];
    password += numbers[Math.floor(Math.random() * numbers.length)];
    password += symbols[Math.floor(Math.random() * symbols.length)];

    // completa até 12 caracteres com caracteres aleatórios
    const allChars = lowercase + uppercase + numbers + symbols;
    for (let i = password.length; i < 12; i++) {
      password += allChars[Math.floor(Math.random() * allChars.length)];
    }

    // embaralhar a senha
    return password.split('').sort(() => Math.random() - 0.5).join('');
  }

  /**
 * Cria um novo usuário automaticamente via SSO Microsoft
 */
  private async createSSOUser(
    email: string,
    name: string,
    surname: string,
    req: any
  ): Promise<UserDto> {
    this.logger.log(`Criando usuário automaticamente via SSO Microsoft: ${email}`);

    const defaultCustomerId = process.env.SSO_DEFAULT_CUSTOMER_ID;

    if (!defaultCustomerId) {
      throw new HttpException(
        'Configuração de customer padrão para SSO não encontrada. Configure a variável SSO_DEFAULT_CUSTOMER_ID',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }

    const [defaultCustomer, customerError] = await to<CustomerEntity, Error>(
      this.manager.findOneBy(CustomerEntity, { id: defaultCustomerId })
    );

    if (customerError || !defaultCustomer) {
      throw new HttpException(
        `Customer padrão não encontrado: ${defaultCustomerId}. Verifique se o ID está correto.`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }

    const temporaryPassword = this.generateSecureTemporaryPassword();

    const newUser: Partial<UserDto> = {
      email,
      password: temporaryPassword,
      userName: email.split('@')[0],
      name: name || email.split('@')[0],
      surname: surname || '',
      address: '',
      phoneNumber: '',
      country: 'BR',
      isSuspended: false,
      isTwoFactorAuthenticationEnabled: false,
      twoFactorAuthenticationSecret: '',
      mustChangePassword: true,
      customers: [defaultCustomer],
      isEmailConfirmed: true,
      isDev: false,
      isMaster: false,
      lastPasswordUpdate: new Date(),
    };

    const userSystem = await this.manager.findOneBy(UserEntity, { email: '<EMAIL>' });

    const mockRequest = {
      headers: {
        'client-ip': req.headers['client-ip'],
        'user-agent': req.headers['user-agent'],
        'partner-id': defaultCustomerId
      },
      user: {
        id: userSystem.id,
        email: userSystem.email,
        customers: [defaultCustomer]
      }
    } as unknown as IRequestWithUser;

    try {
      const createResult = await this.usersService.createUser(newUser, mockRequest);

      if (!createResult || (typeof createResult === 'object' && 'message' in createResult)) {
        throw new Error('Falha na criação do usuário');
      }

      const createdUser = await this.manager.findOneBy(UserEntity, { email: email });

      if (!createdUser) {
        throw new HttpException(
          'Erro ao recuperar usuário criado via SSO',
          HttpStatus.INTERNAL_SERVER_ERROR
        );
      }      

      // adiciona log de criação do usuário
      const log = new LogEntity();
      log.userId = createdUser.id;
      log.ip = req.headers['client-ip'];
      log.field = '';
      log.oldValue = '';
      log.newValue = 'MICROSOFT_SSO';
      log.user = createdUser;
      log.userDidId = userSystem.id;
      log.updatedAt = new Date();
      log.entity = 'users';

      await this.manager.save(log);

      this.logger.log(`Usuário ${email} criado com sucesso via SSO Microsoft. ID: ${createdUser.id}`);
      this.logger.log(`Senha temporária gerada. Usuário deve alterar no primeiro login normal.`);

      return createdUser;

    } catch (error) {
      this.logger.error(`Erro ao criar usuário ${email} via SSO: ${error.message}`);
      throw new HttpException(
        `Erro ao criar usuário automaticamente via SSO: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  // async registerNewUser(authRegisterUserDto: Partial<UserDto>) {
  //   const hashId = crypto.createHash('md5').update(authRegisterUserDto.email).digest("hex")
  //   const hashedPassword = await hash(authRegisterUserDto.password, 10);
  //   const newUser = {
  //     id: hashId,
  //     email: authRegisterUserDto.email,
  //     userName: authRegisterUserDto.userName,
  //     name: authRegisterUserDto.name,
  //     surname: authRegisterUserDto.surname,
  //     country: authRegisterUserDto.country,
  //     state: authRegisterUserDto.state,
  //     password: hashedPassword,
  //     address: authRegisterUserDto.address,
  //     phoneNumber: authRegisterUserDto.phoneNumber,
  //     lastLoginAt: new Date(),
  //     twoFactorAuthenticationSecret: null,
  //     isTwoFactorAuthenticationEnabled: false,
  //     isSuspended: false
  //   }
  //   return await this.usersService.createUser(newUser)
  // }
}
