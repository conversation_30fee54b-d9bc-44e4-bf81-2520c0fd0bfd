import { CustomerDto } from "@/modules/customers/dto/customer.dto";
import { PermissionsDto } from "@/modules/permissions/dto/permissions.dto";
import { UpdateGroupDto } from "@/modules/permissions/dto/update-permission-groups.dto";

export interface User {
  id: string;
  userId?: string;
  email: string;
  userName: string;
  name: string;
  password: string;
  mustChangePassword?: boolean;
  twoFactorAuthenticationSecret: string;
  isTwoFactorAuthenticationEnabled: boolean;
  permissions?: PermissionsDto[];
  permissionGroups?: UpdateGroupDto[];
  customers?: CustomerDto[];
  isMaster?: boolean;
  lastPasswordUpdate?: Date;
  isDev?: boolean;
}
