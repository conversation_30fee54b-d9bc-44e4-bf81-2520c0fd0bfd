import { ExtractJwt, Strategy } from 'passport-jwt';
import { PassportStrategy } from '@nestjs/passport';
import { Injectable } from '@nestjs/common';
import { UsersService } from '@/modules/users/users.service';
import { TokenPayload } from '@/modules/authentication/entities/token-payload.entity';
import 'dotenv/config';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(private readonly userService: UsersService) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      secretOrKey: process.env.JWT_SECRET,
      passReqToCallback: true,
    });
  }

  async validate(request: any, payload: TokenPayload) {
    const user = await this.userService.findOneUser(payload.email);
    if (user) {
      if (request?.originalUrl === '/authentication/2fa/generate') {
        if (user.isTwoFactorAuthenticationEnabled) {
          return false;
        }
      }
      return user;
    }
  }
}
