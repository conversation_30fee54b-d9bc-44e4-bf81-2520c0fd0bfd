import {
  Body,
  Controller,
  Get,
  HttpCode,
  Post,
  Request,
  Response,
  UnauthorizedException,
  UseGuards,
  Headers,
} from "@nestjs/common";
import { AuthenticationService } from "@/modules/authentication/authentication.service";
import { LocalAuthGuard } from "@/common/guards/local/local-auth.guard";
import { JwtAuthGuard } from "@/common/guards/jwt/jwt-auth.guard";
import { UsersService } from "@/modules/users/users.service";
import { Jwt2faAuthGuard } from "@/common/guards/jwt-2fa/jwt-2fa-auth.guard";
import { PermissionsGuard } from "@/common/guards/rules/permission.guard";
import { ApiBearerAuth, ApiBody, ApiHeader, ApiResponse, ApiTags } from "@nestjs/swagger";
import { IpGroupsGuard } from "@/common/guards/rules/ip-gropus.guard";
import { AuthValidator } from "./dtos/auth-validator";
import { MicrosoftSSODto } from './dtos/microsoft-sso.dto';

@ApiTags("Authentication")
@ApiBearerAuth("access-token")
@Controller("v1/pam/authentication")
export class AuthenticationController {
  constructor(
    private readonly authenticationService: AuthenticationService,
    private usersService: UsersService
  ) {}

  @UseGuards(LocalAuthGuard)
  @Post("login")
  @HttpCode(405)
  @ApiBody({
    schema: {
      type: "object",
      properties: {
        email: {
          type: "string",
        },
        password: {
          type: "string",
        },
      },
    },
  })
  async login(@Request() req) {
    const validationUser = req.user;
    return this.authenticationService.login(
      validationUser,
      req,
      validationUser.settings
    );
  }

  @Post("2fa/generate")
  @UseGuards(JwtAuthGuard)
  @HttpCode(200)
  @ApiResponse({ status: 200, description: "Success.", type: String })
  @ApiHeader({ name: "is_pam", required: false })
  async register(@Response() response, @Request() request) {
    const isPam = request.headers['is_pam'];
    const { otpAuthUrl } =
      await this.authenticationService.generateTwoFactorAuthenticationSecret(
        request.user,
        isPam
      );

    return response.json(
      await this.authenticationService.generateQrCodeDataURL(otpAuthUrl)
    );
  }

  @Post("2fa/turn-on")
  @UseGuards(JwtAuthGuard)
  @ApiBody({
    schema: {
      type: "object",
      properties: {
        twoFactorAuthenticationCode: {
          type: "string",
        },
      },
    },
  })
  async turnOnTwoFactorAuthentication(@Request() request, @Body() body) {
    const isCodeValid =
      this.authenticationService.isTwoFactorAuthenticationCodeValid(
        body.twoFactorAuthenticationCode,
        request.user
      );
    if (!isCodeValid) {
      throw new UnauthorizedException("Wrong authentication code");
    }
    await this.usersService.turnOnTwoFactorAuthentication(request.user.id);
    return { status: "success" };
  }

  @Post("2fa/authenticate")
  @HttpCode(200)
  @UseGuards(JwtAuthGuard)
  @ApiBody({
    schema: {
      type: "object",
      properties: {
        twoFactorAuthenticationCode: {
          type: "string",
        },
      },
    },
  })
  async authenticate(@Request() request, @Body() body) {
    const isCodeValid =
      this.authenticationService.isTwoFactorAuthenticationCodeValid(
        body.twoFactorAuthenticationCode,
        request.user
      );

    if (!isCodeValid) {
      throw new UnauthorizedException("Wrong authentication code");
    }

    return this.authenticationService.loginWith2fa(request.user, request);
  }

  @Post("token/validator")
  @HttpCode(200)
  @UseGuards(JwtAuthGuard)
  tokenValidator(@Headers("authorization") bearer?: string) {
    try {
      if (!bearer) {
        throw new UnauthorizedException("Token não fornecido.");
      }

      const token = bearer.startsWith("Bearer ") ? bearer.slice(7) : bearer;

      const validToken = this.authenticationService.validateToken(token);

      if (!validToken) {
        return { validToken: false };
      }

      return { validToken: true };
    } catch {
      throw new UnauthorizedException("Token inválido.");
    }
  }

  @Post("2fa/validator")
  @HttpCode(200)
  @UseGuards(AuthValidator)
  async validator(@Body() body) {
    const validToken = await this.authenticationService.validateToken(
      body.bearer
    );

    if (validToken) {
      let validIp;
      //validPermission = false;
      if (body.ip) {
        validIp = await this.usersService.checkIp(
          body.partnerId,
          validToken.userId,
          body.ip
        );
      }

      if (body.path && body.method) {
        // validPermission = await this.usersService.checkPermissions(
        //   validToken.userId,
        //   body.path,
        //   body.method,
        //   body.partnerId
        // );
      }
      return { validToken, validIp, validPermission: true };
    }

    return validToken;
  }

  //@UseGuards(LocalAuthGuard)
  @Post("microsoft-sso")
  @HttpCode(405)
  @ApiBody({ type: MicrosoftSSODto })
  @ApiResponse({ 
    status: 405, 
    description: "Login SSO Microsoft realizado com sucesso" 
  })
  @ApiResponse({ 
    status: 401, 
    description: "Token inválido ou usuário não autorizado" 
  })
  async microsoftSSO(@Body() microsoftTokenData: MicrosoftSSODto, @Request() req) {
    return this.authenticationService.validateMicrosoftToken(microsoftTokenData, req);
  }

  // @Post('/signup')
  // @HttpCode(200)
  // @UseGuards(Jwt2faAuthGuard)
  // @UsePipes(ValidationPipe)
  // async signup(@Body() authRegisterUserDto: UserDto) {
  //   return await this.authenticationService.registerNewUser(authRegisterUserDto);
  // }

  @Get("teste")
  @HttpCode(200)
  @UseGuards(Jwt2faAuthGuard)
  @UseGuards(PermissionsGuard)
  async signup() {
    return "success";
  }

  @Post("teste")
  @HttpCode(200)
  @UseGuards(Jwt2faAuthGuard)
  @UseGuards(PermissionsGuard)
  @UseGuards(IpGroupsGuard)
  async test() {
    return "success";
  }
}
