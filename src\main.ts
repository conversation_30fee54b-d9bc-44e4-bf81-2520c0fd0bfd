import { NestFactory } from "@nestjs/core";
import { SwaggerModule, DocumentBuilder } from "@nestjs/swagger";
import { AppModule } from "@/app.module";
import "dotenv/config";
import { HttpException, HttpStatus, ValidationError, ValidationPipe } from "@nestjs/common";
import { addGlobalResponses } from "./common/swagger/swagger-response-global";

async function bootstrap() {
  const app = await NestFactory.create(AppModule, {cors: true});
  app.enableCors({
    origin: [
      "https://backoffice.pam.qa.h2.opah.com.br",
      "https://backoffice.staging.complianceservice.app",
      "https://backoffice.complianceservice.app", 
      "https://backoffice.onbd.h2.opah.com.br", 
      "https://api.betsystem.app", 
      "https://api.staging.betsystem.app",
      "https://backoffice.pam.h2.opah.com.br",
      "https://api.h2.opah.com.br",
      "http://localhost:5173",
    ],
    methods:['GET','HEAD','PUT','PATCH','POST','DELETE', 'OPTIONS'],
    allowedHeaders: [
      'Content-Type',
      'Authorization',
      'Accept',
      'X-Requested-With',
      'Access-Control-Allow-Origin',
      'Access-Control-Allow-Headers',
      'Access-Control-Allow-Methods',
      'Access-Control-Allow-Credentials',
      'Origin',
      'Referer',
      'User-Agent',
      'partner-id', 
      'client-ip'
    ],
    credentials: true,
  });
  app.useGlobalPipes(
    new ValidationPipe(
      {
        exceptionFactory: (errors: ValidationError[]) => {
          const messages = errors.map((error) => {
            return ` ${Object.values(error.constraints).join(", ")}`
          });
          console.error(messages.toString());
          throw new HttpException(messages.toString(), HttpStatus.BAD_REQUEST);
        }
      }
    ),
  );
  const config = new DocumentBuilder()
    .setTitle("PAM H2")
    .setDescription("The API description")
    .setVersion("1.0")
    .addBearerAuth(
      {
        description: `Insira apenas o token, sem aspas.`,
        name: "Authorization",
        bearerFormat: "Bearer",
        scheme: "Bearer",
        type: "http",
        in: "Header",
      },
      "access-token",
    )
    .build();
  const document = SwaggerModule.createDocument(app, config, {
    extraModels: [],
    deepScanRoutes: true,
  });

  addGlobalResponses(document);

  SwaggerModule.setup("v1/pam/user-admin/doc", app, document);
  await app.listen(process.env.APP_PORT);
  console.log("PORT:", process.env.APP_PORT);
}
bootstrap();
