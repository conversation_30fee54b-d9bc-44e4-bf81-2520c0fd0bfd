import {
  CanActivate,
  ExecutionContext,
  HttpException,
  HttpStatus,
  Injectable,
} from "@nestjs/common";
import { AuthenticationService } from "@/modules/authentication/authentication.service";
import { UsersService } from "@/modules/users/users.service";

@Injectable()
export class PermissionsGuard implements CanActivate {
  constructor(
    private authenticationService: AuthenticationService,
    private usersService: UsersService
  ) {}
  async canActivate(context: ExecutionContext) {
    const request = context.switchToHttp().getRequest();
    if (!request.headers.authorization)
      throw new HttpException(
        "Authorization not found",
        HttpStatus.FAILED_DEPENDENCY
      );

    const { userId } = this.authenticationService.validateToken(
      request.headers.authorization
    );

    if (!request.headers["partner-id"])
      throw new HttpException(
        "Partner Id not found",
        HttpStatus.FAILED_DEPENDENCY
      );

    return await this.usersService.checkPermissions(
      userId,
      request.route?.path,
      request.route?.stack[0]?.method,
      request.headers["partner-id"]
    );
  }
}
