import { Column, CreateDateColumn, DeleteDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn } from "typeorm";

@Entity({ name: 'partner_ip_restrictions', schema: 'partners' })
export class PartnerIpRestrictions {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'id_partner', type: 'uuid' })
  idPartner: string;

  @Column({ name: 'ip_address', type: 'inet' })
  ipAddress: string;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  updatedAt: Date

  @DeleteDateColumn({ name: 'deleted_at', type: 'timestamptz' })
  deletedAt?: Date
}