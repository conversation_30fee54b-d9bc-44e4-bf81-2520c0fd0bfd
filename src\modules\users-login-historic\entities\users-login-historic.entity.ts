import { Column, Entity, PrimaryGeneratedColumn } from "typeorm";

@Entity("users_login_historic")
export class UsersLoginHistoricEntity {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ name: "user_id" })
  userId: string;

  @Column({ name: "email" })
  email: string;

  @Column({ name: "ip", nullable: true })
  ip: string;

  @Column({ name: "device", nullable: true })
  deviceInfo: string;

  @Column({ name: "is_match" })
  isMatch: boolean;

  @Column({
    name: "attempted_at",
  })
  attemptedAt: Date;

  @Column({ name: "is_sso", nullable: true })
  is_sso: boolean;
}
