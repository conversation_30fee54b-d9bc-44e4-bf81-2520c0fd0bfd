import {
  HttpException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { PermissionsDto } from '@/modules/permissions/dto/permissions.dto';
import { PermissionsEntity } from '@/modules/permissions/entities/permissions.entity';
import { GroupEntity } from '@/modules/permissions/entities/permission-groups.entity';
import { CreateGroupDto } from '@/modules/permissions/dto/create-permission-groups.dto';
import {
  EntityManager,
  FindOptionsWhere,
  In,
  InsertResult,
  Like,
  UpdateResult,
} from 'typeorm';
import { InjectEntityManager } from '@nestjs/typeorm';
import { UpdateGroupDto } from '@/modules/permissions/dto/update-permission-groups.dto';
import { UserEntity } from '@/modules/users/entities/user.entity';
import { to } from '@/common/utils/to';
import { UserDto } from '@/modules/users/dtos/user.dto';
import { SortOrder } from '@/common/filters/sortOrder';
import { CustomerUserEntity } from '../customers/entities/customer-user.entity';
import { CustomerEntity } from '../customers/entities/customer.entity';
import { UsersCustomersHasPermissionsEntity } from './entities/users-customers-has-permission.entity';
import { UsersPermissionGroupsEntity } from './entities/users_permission_groups.entity';
import { UpdatePermissionGroupsDto } from './dto/users_permission_groups.dto';
import {
  getPage,
  getPageSize,
  getSkip,
  getTake,
  getTotalPage,
} from '@/common/utils/pagination.utils';
import { FilterPermissionGroupDto } from './dto/filter-permission-group.dto';
import { CategoryPermissionEntity } from './entities/category_permission';
import {
  CategoryPermissionDto,
  CreateCategoryPermissionDto,
} from './dto/category_permission.dto';

@Injectable()
export class PermissionsService {
  constructor(@InjectEntityManager() private readonly manager: EntityManager) {}

  async createGroup(createGroupDto: CreateGroupDto, req) {
    const [permissions, err] = await to<PermissionsEntity[], Error>(
      this.manager.findBy(PermissionsEntity, {
        id: In(createGroupDto.permissions.map(p => p.id)),
      }),
    );
    const [groups, groupsError] = await to<GroupEntity[], Error>(
      this.manager.find(GroupEntity,{
        where: { name: createGroupDto.name },}),
    )
    if(groupsError) throw new HttpException(groupsError.message, HttpStatus.INTERNAL_SERVER_ERROR);
    if (groups.length > 0) throw new HttpException('Nome do grupo já cadastrado', HttpStatus.BAD_REQUEST);
    if (err)
      throw new HttpException(err.message, HttpStatus.INTERNAL_SERVER_ERROR);
    if (!permissions)
      throw new HttpException('Permissão não encontrada', HttpStatus.NOT_FOUND);

      const customer = req.headers['partner-id'];
      const group = new GroupEntity();
      group.name = createGroupDto.name;
      group.permissions = permissions;
      group.customers_id = customer;

      const [, error] = await to<GroupEntity, Error>(this.manager.save(group));
      if (error)
        throw new HttpException(
          error.message,
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
  }

  async findAllPermissions(): Promise<PermissionsDto[]> {
    const [permissions, error] = await to<PermissionsDto[], Error>(
      this.manager.find(PermissionsEntity),
    );
    if (error)
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    return permissions;
  }

  async findUserWithPermissions(id: string): Promise<UserDto> {
    const [user, error] = await to<UserDto, Error>(
      this.manager.findOneBy(UserEntity, { id }),
    );
    if (error)
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    if (!user) throw new HttpException('User not found', HttpStatus.NOT_FOUND);
    return user;
  }

  async findOneById(id: string): Promise<PermissionsDto> {
    const [permission, error] = await to<PermissionsDto, Error>(
      this.manager.findOneBy(PermissionsEntity, { id }),
    );
    if (error)
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    return permission;
  }

  async findOneGroupById(id: string): Promise<UpdateGroupDto> {
    const [group, error] = await to<UpdateGroupDto, Error>(
      this.manager.findOne(GroupEntity, {
        where: { id: id },
        relations: { permissions: true },
      }),
    );
    if (error)
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    return group;
  }

  async findAllGroups(filter: FilterPermissionGroupDto): Promise<{
    data: UpdateGroupDto[];
    totalItems: number;
    totalPages: number;
    currentPage: number;
    pageSize: number;
  }> {
    if (!filter.sortOrder) {
      filter.sortOrder = SortOrder.DESC;
    }

    const page = getPage(filter.page); 
    const pageSize = getPageSize(filter.pageSize);
    const customers = filter.customers ? filter.customers.split(',') : [''];

    const where:
      | FindOptionsWhere<GroupEntity>
      | FindOptionsWhere<GroupEntity>[] = {
      customers_id: In(customers),
    };

    if (filter?.name) {
      where.name = Like(`%${filter.name}%`);
    }

    const totalItems = await this.manager.count(GroupEntity, { where });
    const skip = getSkip((page - 1) * pageSize, totalItems);
    const take = getTake(pageSize, totalItems, skip);
    const totalPages = getTotalPage(totalItems, pageSize);

    const [data, error] = await to<GroupEntity[], Error>(
      this.manager.find(GroupEntity, {
        select: {
          id: true,
          name: true,
          updatedAt: true,
          createdAt: true,
        },
        relations: {
          permissions: true,
        },
        where,
        skip,
        take,
        order: { name: filter.sortOrder },
      }),
    );

    if (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    return {
      data,
      totalItems,
      totalPages,
      currentPage: page,
      pageSize,
    };
  }

  async removeGroup(id: string) {
    const [, error] = await to<UpdateResult, Error>(
      this.manager.softDelete(GroupEntity, { id }),
    );
    if (error)
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    return;
  }

  async updateGroup(id: string, updateGroupDto: Partial<UpdateGroupDto>) {
    try {
      const group = await this.findOneGroupById(id);

      if (!group) {
        throw new Error(`Group with ID ${id} not found`);
      }

      if (updateGroupDto.name) {
        group.name = updateGroupDto.name;
      }

      if (updateGroupDto.permissions?.length > 0) {
        const permissionPromises = updateGroupDto.permissions.map(
          async item => {
            const permission = await this.findOneById(item.id);
            if (!permission) {
              throw new Error(`Permission with ID ${item.id} not found`);
            }
            return permission;
          },
        );

        group.permissions = await Promise.all(permissionPromises);
      } else {
        group.permissions = [];
      }

      group.updatedAt = new Date();

      const [updated, error] = await to<UpdateGroupDto, Error>(
        this.manager.save(GroupEntity, group),
      );

      if (error) {
        throw new HttpException(
          error.message,
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }

      return updated;
    } catch (error) {
      throw new Error(`Failed to update group: ${error.message}`);
    }
  }

  async updatePermissionUsers(
    id: string,
    permissions: Pick<PermissionsDto, 'id'>[],
    partnerId: string,
  ) {
    const [user, userError] = await to<UserEntity, Error>(
      this.manager.findOne(UserEntity, {
        where: { id: id },
        relations: { customers: true },
      }),
    );

    if (userError || !user)
      throw new HttpException(
        userError?.message || 'User not found',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );

    const [customerEntity, customersError] = await to<CustomerEntity, Error>(
      this.manager.findOneBy(CustomerEntity, { id: partnerId }),
    );

    if (!customerEntity || customersError) {
      throw new HttpException(
        userError?.message || 'Customers not found.',
        HttpStatus.NOT_FOUND,
      );
    }

    const customerUser = await this.manager.findOne(CustomerUserEntity, {
      where: {
        user: user,
        customers: customerEntity,
      },
      relations: {
        customers: true,
        user: true,
      },
    });

    const permissionEntities = await this.manager.find(PermissionsEntity, {
      where: permissions.map(permission => ({ id: permission.id })),
    });

    if (permissionEntities.length !== permissions.length) {
      throw new HttpException(
        'Some Permitions not found',
        HttpStatus.NOT_FOUND,
      );
    }

    const usersCustomersHasPermissionsList = permissions.map(permission => {
      const permissionEntity = permissionEntities.find(
        p => p.id === permission.id,
      );
      return this.manager.create(UsersCustomersHasPermissionsEntity, {
        customerUser: customerUser,
        permissions: permissionEntity,
      });
    });

    const queryRunner = this.manager.connection.createQueryRunner();
    await queryRunner.startTransaction();

    try {
      const [insertedEntities, saveError] = await to<InsertResult, Error>(
        queryRunner.manager.insert(
          UsersCustomersHasPermissionsEntity,
          usersCustomersHasPermissionsList,
        ),
      );

      if (saveError) {
        throw new HttpException(
          saveError.message,
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }

      await queryRunner.commitTransaction();
      return insertedEntities;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    } finally {
      await queryRunner.release();
    }
  }

  async updateGroupUsers_old(
    id: string,
    groups: UpdateGroupDto[],
    partnerId: string,
  ) {
    const [user, userError] = await to<UserEntity, Error>(
      this.manager.findOne(UserEntity, {
        where: { id: id },
        relations: { customers: true, permissionGroups: true },
      }),
    );

    if (userError || !user) {
      throw new HttpException(
        userError?.message || 'User not found',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }

    const [customerEntity, customersError] = await to<CustomerEntity, Error>(
      this.manager.findOneBy(CustomerEntity, { id: partnerId }),
    );

    if (!customerEntity || customersError) {
      throw new HttpException(
        userError?.message || 'Customer not found.',
        HttpStatus.NOT_FOUND,
      );
    }

    const permissionsToUpdate: Pick<PermissionsDto, 'id'>[] = [];

    for await (const group of groups) {
      const groupEntity = await this.manager.findOneBy(GroupEntity, {
        id: group.id,
      });

      if (!groupEntity) {
        throw new HttpException('Group not found', HttpStatus.NOT_FOUND);
      }

      const userGroupEntity = await this.manager.findOneBy(
        UsersPermissionGroupsEntity,
        { permission_groups_id: group.id, users_id: user.id },
      );

      const hasGroup = userGroupEntity
        ? userGroupEntity.permission_groups_id === group.id
        : false;

      if (!hasGroup) {
        user.permissionGroups = [...user.permissionGroups, groupEntity];

        const groupPermissions = await this.manager.find(PermissionsEntity, {
          where: { groups: groupEntity },
        });
        groupPermissions.forEach(permission => {
          permissionsToUpdate.push({
            id: permission.id,
          });
        });

        await this.manager.insert(UsersPermissionGroupsEntity, {
          users_id: user.id,
          permission_groups_id: group.id,
        });
      }
    }

    if (permissionsToUpdate.length > 0) {
      await this.updatePermissionUsers(user.id, permissionsToUpdate, partnerId);
    }
  }

  async setPermissionGroupsToUser(
    groups: UpdatePermissionGroupsDto[],
    userId: string,
  ) {
    // 1. Verifica se o usuário existe
    const userExists = await this.manager.findOne(UserEntity, {
      where: { id: userId },
    });
    if (!userExists) {
      throw new NotFoundException(`User with ID ${userId} not found`);
    }

    // 2. Verifica se todos os grupos de permissão existem
    const groupIds = groups.map(group => group.id);
    const existingGroups = await this.manager.findBy(GroupEntity, {
      id: In(groupIds),
    });
    if (existingGroups.length !== groups.length) {
      throw new NotFoundException('One or more permission groups not found');
    }

    // 3. Soft delete dos grupos de permissão existentes do usuário
    await this.manager.update(
      UsersPermissionGroupsEntity,
      { users_id: userId },
      { deletedAt: new Date() },
    );
    // 4. Cria novas instâncias de UsersPermissionGroupsEntity para cada novo grupo de permissão usando `create`
    const newGroups = groups.map(group =>
      this.manager.create(UsersPermissionGroupsEntity, {
        users_id: userId,
        permission_groups_id: group.id,
        deletedAt: null,
      }),
    );

    // 5. Salva as novas permissões na tabela intermediária
    try {
      const updated = await this.manager.save(
        UsersPermissionGroupsEntity,
        newGroups,
      );
      return updated;
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async updateGroupUsers(
    userId: string,
    groups: UpdateGroupDto[],
    partnerId: string,
  ): Promise<void> {
    await this.manager.delete(UsersPermissionGroupsEntity, {
      users_id: userId,
    });

    const validGroups = groups.filter(group => Object.keys(group).length > 0);

    const dataInsertGroups: Partial<UsersPermissionGroupsEntity>[] = [];

    for await (const group of validGroups) {
      dataInsertGroups.push({
        users_id: userId,
        permission_groups_id: group.id,
      });
    }

    await this.manager.insert(UsersPermissionGroupsEntity, dataInsertGroups);

    // const permissionsToUpdate: Pick<PermissionsDto, 'id'>[] = [];

    // for (const groupDto of validGroups) {
    //   const group = await this.findGroupOrThrow(groupDto.id);

    //   const alreadyInGroup = await this.isUserInGroup(user.id, group.id);

    //   if (!alreadyInGroup) {
    //     user.permissionGroups.push(group);

    //     const groupPermissions = await this.manager.find(PermissionsEntity, {
    //       where: { groups: group },
    //     });

    //     permissionsToUpdate.push(
    //       ...groupPermissions.map(permission => ({ id: permission.id })),
    //     );

    //     await this.manager.insert(UsersPermissionGroupsEntity, {
    //       users_id: user.id,
    //       permission_groups_id: group.id,
    //     });
    //   }
    // }

    // if (permissionsToUpdate.length > 0) {
    //   await this.updatePermissionUsers(user.id, permissionsToUpdate, partnerId);
    // }
  }

  async findCategories() {
    const [data, error] = await to<CategoryPermissionDto[], Error>(
      this.manager.find(CategoryPermissionEntity),
    );

    if (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    return data;
  }

  async createCategoryPermission(data: CreateCategoryPermissionDto) {
    const category = new CategoryPermissionEntity();
    category.name = data.name;
    category.description = data.description;
    category.active = data.active;

    const [, error] = await to<CategoryPermissionEntity, Error>(
      this.manager.save(category),
    );

    if (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async deleteCategoryPermisson(id: string): Promise<void> {
    const [, error] = await to<UpdateResult, Error>(
      this.manager.softDelete(CategoryPermissionEntity, { id }),
    );

    if (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    return;
  }

  /*
   * metodos privados
   */
  private async findUserOrThrow(userId: string): Promise<UserEntity> {
    const [user, error] = await to<UserEntity, Error>(
      this.manager.findOne(UserEntity, {
        where: { id: userId },
        relations: { customers: true, permissionGroups: true },
      }),
    );

    if (!user || error) {
      throw new HttpException(
        error?.message || 'User not found',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }

    return user;
  }

  private async findCustomerOrThrow(
    customerId: string,
  ): Promise<CustomerEntity> {
    const [customer, error] = await to<CustomerEntity, Error>(
      this.manager.findOneBy(CustomerEntity, { id: customerId }),
    );

    if (!customer || error) {
      throw new HttpException(
        error?.message || 'Customer not found',
        HttpStatus.NOT_FOUND,
      );
    }

    return customer;
  }

  private async findGroupOrThrow(groupId: string): Promise<GroupEntity> {
    const group = await this.manager.findOneBy(GroupEntity, { id: groupId });

    if (!group) {
      throw new HttpException('Group not found', HttpStatus.NOT_FOUND);
    }

    return group;
  }

  private async isUserInGroup(
    userId: string,
    groupId: string,
  ): Promise<boolean> {
    const userGroup = await this.manager.findOneBy(
      UsersPermissionGroupsEntity,
      {
        users_id: userId,
        permission_groups_id: groupId,
      },
    );

    return !!userGroup;
  }
}
