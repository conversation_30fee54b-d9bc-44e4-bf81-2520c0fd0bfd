import { Module } from '@nestjs/common';
import { PermissionsService } from '@/modules/permissions/permissions.service';
import { permissionsProviders } from '@/modules/permissions/permissions.provider';
import { DatabaseModule } from '@/common/database/users/database.module';
import { PermissionsController } from '@/modules/permissions/permissions.controller';
import { groupProviders } from '@/modules/permissions/group.providers';
import { usersProviders } from '@/modules/users/users.provider';
import { UsersService } from '@/modules/users/users.service';
import { UserEntity } from '@/modules/users/entities/user.entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { GroupEntity } from '@/modules/permissions/entities/permission-groups.entity';
import { PermissionsEntity } from '@/modules/permissions/entities/permissions.entity';
import { CustomersService } from '../customers/customers.service';
import { MailModule } from '../mail/mail.module';
import { AuthenticationModule } from '../authentication/authentication.module';
import { PartnerSettingsModule } from '../partner-settings/partner-settings.module';
import { PermissionService } from './services/permission.service';
import { UsersModule } from '../users/users.module';

@Module({
  imports: [
    DatabaseModule,
    AuthenticationModule,
    TypeOrmModule.forFeature([UserEntity]),
    TypeOrmModule.forFeature([GroupEntity]),
    TypeOrmModule.forFeature([PermissionsEntity]),
    MailModule,
    PartnerSettingsModule,
    UsersModule,
  ],
  providers: [
    ...groupProviders,
    ...permissionsProviders,
    ...usersProviders,
    PermissionsService,
    UsersService,
    CustomersService,
    PermissionService,
  ],
  controllers: [PermissionsController],
  exports: [PermissionsService],
})
export class PermissionsModule {}
