import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  HttpCode,
  UseGuards,
  UsePipes,
  ValidationPipe,
  Req,
} from "@nestjs/common";
import { CustomersService } from "./customers.service";
import { Jwt2faAuthGuard } from "../../common/guards/jwt-2fa/jwt-2fa-auth.guard";
import { CustomerDto } from "./dto/customer.dto";
import { CustomerUserDto } from "./dto/customer-user.dto";
import { IpGroupsGuard } from "@/common/guards/rules/ip-gropus.guard";
import { PermissionsGuard } from "@/common/guards/rules/permission.guard";
import { ApiBearerAuth, ApiResponse, ApiTags } from "@nestjs/swagger";

@ApiTags("Customers")
@ApiBearerAuth("access-token")
@Controller("v1/pam/customers")
@UseGuards(PermissionsGuard)
@UseGuards(IpGroupsGuard)
export class CustomersController {
  constructor(private readonly customersService: CustomersService) {}

  @Post()
  @UseGuards(Jwt2faAuthGuard)
  @UsePipes(ValidationPipe)
  @HttpCode(200)
  @ApiResponse({ status: 200, description: 'Success.' })
  async createCustomer(@Body() customer: CustomerDto) {
    return await this.customersService.createCustomer(customer);
  }

  @Post("customers-users")
  @UseGuards(Jwt2faAuthGuard)
  @UsePipes(ValidationPipe)
  @HttpCode(200)
  @ApiResponse({ status: 200, description: 'Success.' })
  async createCustomerUser(@Body() customer: CustomerUserDto, @Req() req) {
    return await this.customersService.createCustomerUser(
      customer,
      req,
      customer.user,
    );
  }

  @Get()
  @UseGuards(Jwt2faAuthGuard)
  @UsePipes(ValidationPipe)
  @HttpCode(200)
  @ApiResponse({ status: 200, description: 'Success.', type: [CustomerDto] })
  async findAll(): Promise<CustomerDto[]> {
    return await this.customersService.findAll();
  }

  @Get(":id")
  @UseGuards(Jwt2faAuthGuard)
  @UsePipes(ValidationPipe)
  @HttpCode(200)
  @ApiResponse({ status: 200, description: 'Success.', type: CustomerDto })
  async findOne(@Param("id") id: string): Promise<CustomerDto> {
    return await this.customersService.findOneById(id);
  }

  @Delete(":id")
  @UseGuards(Jwt2faAuthGuard)
  @UsePipes(ValidationPipe)
  @HttpCode(200)
  @ApiResponse({ status: 200, description: 'Success.' })
  remove(@Param("id") id: string) {
    return this.customersService.remove(id);
  }
}
