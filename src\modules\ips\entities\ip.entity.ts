import {
  Column,
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  ManyToOne,
  <PERSON>inColumn,
  DeleteDateColumn,
} from "typeorm";
import { IpGroupEntity } from "@/modules/group/entities/group.entity";

@Entity("ips")
export class IpEntity {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ type: "varchar", length: 255 })
  ip: string;

  @ManyToOne(() => IpGroupEntity, (group) => group.ips, { onDelete: "CASCADE" })
  @JoinColumn({ name: "ip_groups_id" })
  group: IpGroupEntity;

  @Column({ type: "tinyint", default: 1 })
  is_activated: boolean | number;

  @DeleteDateColumn()
  deletedAt?: Date;
}
