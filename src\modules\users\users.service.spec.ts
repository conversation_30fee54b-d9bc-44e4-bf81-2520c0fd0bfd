import { Test, TestingModule } from "@nestjs/testing";
import { UsersService } from "./users.service";
import { TypeOrmModule } from "@nestjs/typeorm";
import { UserEntity } from "./entities/user.entity";
import { DataSource, QueryRunner } from "typeorm";
import "dotenv/config";

describe("UsersService", () => {
  let service: UsersService;
  let dataSource: DataSource;
  let queryRunner: QueryRunner;
  let createQueryRunner;
  let release;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        TypeOrmModule.forFeature([UserEntity]),
        TypeOrmModule.forRoot({
          type: "postgres",
          host: "localhost",
          port: 5432,
          username: process.env.POSTGRES_USER,
          password: process.env.POSTGRES_PASSWORD,
          database: process.env.POSTGRES_DB,
          entities: [__dirname + "../../**/*.entity{.ts,.js}"],
        }),
      ],
      providers: [UsersService],
    }).compile();

    dataSource = module.get<DataSource>(DataSource);

    queryRunner = dataSource.createQueryRunner();
    await queryRunner.startTransaction();

    dataSource.createQueryRunner = () => queryRunner;
    queryRunner.release = () => Promise.resolve();

    service = module.get<UsersService>(UsersService);
  });

  afterEach(async () => {
    await queryRunner.rollbackTransaction();

    dataSource.createQueryRunner = createQueryRunner;

    if (queryRunner && typeof release === "function") {
      await release.call(queryRunner);
    }
  });
  it("should be defined", async () => {
    await service.createUser({
      id: "3ce3bcf5-0983-498b-9f5c-4bf4b1391d8e",
      email: "<EMAIL>",
      password: "123",
      name: "test",
      userName: "test",
      surname: "test",
      phoneNumber: "test",
      country: "test",
      address: "test",
      isSuspended: false,
      isTwoFactorAuthenticationEnabled: false,
      twoFactorAuthenticationSecret: "test",
    });

    expect(service).toBeDefined();
  });

  it("should return a user", async () => {
    const user = await service.findOneUser("<EMAIL>");
    expect(user).toBeDefined();
  });
});
