{"name": "pam", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "MIT", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "dev": "node --max-old-space-size=6144 node_modules/.bin/nest start --watch", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/src/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@nestjs/axios": "^3.1.2", "@nestjs/common": "^10.4.4", "@nestjs/core": "^10.4.4", "@nestjs/jwt": "^10.2.0", "@nestjs/mapped-types": "*", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.4.4", "@nestjs/swagger": "^7.4.2", "@nestjs/terminus": "^10.2.3", "@nestjs/typeorm": "^10.0.2", "axios": "^1.7.7", "bcrypt": "^5.1.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "dotenv": "^16.4.5", "mysql": "^2.18.1", "node-json-db": "^2.3.0", "otplib": "^12.0.1", "passport": "^0.7.0", "passport-jwt": "^4.0.0", "passport-local": "^1.0.0", "pg": "^8.13.0", "qrcode": "^1.5.0", "reflect-metadata": "^0.2.2", "rimraf": "^6.0.1", "rxjs": "^7.2.0", "typeorm": "^0.3.20", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.13.0", "@nestjs/cli": "^10.4.5", "@nestjs/schematics": "^10.1.4", "@nestjs/testing": "^10.4.4", "@types/bcrypt": "^5.0.2", "@types/express": "^5.0.0", "@types/jest": "29.5.13", "@types/node": "^22.7.3", "@types/passport-local": "^1.0.34", "@types/request-ip": "^0.0.41", "@types/supertest": "^6.0.2", "@typescript-eslint/eslint-plugin": "^8.7.0", "@typescript-eslint/parser": "^8.7.0", "eslint": "^9.13.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "globals": "^15.11.0", "jest": "^29.7.0", "prettier": "^3.3.3", "source-map-support": "^0.5.20", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.2.3", "ts-node": "^10.0.0", "tsconfig-paths": "^4.2.0", "typescript": "^5.6.2", "typescript-eslint": "^8.12.2"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}