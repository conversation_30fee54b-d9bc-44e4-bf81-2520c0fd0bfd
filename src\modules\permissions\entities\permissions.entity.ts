import {
  Column,
  CreateDateColumn,
  Entity,
  ManyToMany,
  DeleteDateColumn,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';
import { GroupEntity } from '@/modules/permissions/entities/permission-groups.entity';
import { UsersCustomersHasPermissionsEntity } from './users-customers-has-permission.entity';

@Entity('permissions')
export class PermissionsEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ nullable: true })
  id_category?: string;

  @Column({ nullable: true })
  id_subcategory?: string;

  @Column({ length: 500 })
  method: string;

  @Column({ length: 100 })
  code?: string;

  @Column({ nullable: true })
  description?: string;

  @Column({ nullable: true })
  endpoint?: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @ManyToMany(() => GroupEntity, group => group.permissions)
  groups?: GroupEntity[];

  @OneToMany(
    () => UsersCustomersHasPermissionsEntity,
    userCustomers => userCustomers.permissions,
  )
  usersCustomersHasPermissions: UsersCustomersHasPermissionsEntity[];

  @DeleteDateColumn()
  deletedAt?: Date;
}
