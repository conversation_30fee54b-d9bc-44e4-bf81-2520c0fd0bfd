import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, PrimaryGeneratedColumn } from "typeorm";
import { PermissionsEntity } from "./permissions.entity";
import { CustomerUserEntity } from "@/modules/customers/entities/customer-user.entity";

@Entity("users_customers_has_permissions")
export class UsersCustomersHasPermissionsEntity {
    @PrimaryGeneratedColumn("uuid")
    id: string;

    @ManyToOne(() => PermissionsEntity, (permission) => permission.id)
    @JoinColumn({ name: "permissions_id" })
    permissions: PermissionsEntity

    @ManyToOne(() => CustomerUserEntity, (customerUser) => customerUser.id, )
    @JoinColumn({ name: "users_customers_id" })
    customerUser: CustomerUserEntity
}