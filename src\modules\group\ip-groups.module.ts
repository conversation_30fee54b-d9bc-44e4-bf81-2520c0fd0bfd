import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { IpGroupEntity } from "./entities/group.entity"; // Atualize o caminho
import { GroupsController } from "./groups.controller";
import { GroupsService } from "./groups.service";
import { AuthenticationModule } from "../authentication/authentication.module";
import { UsersModule } from "../users/users.module";

@Module({
  imports: [TypeOrmModule.forFeature([IpGroupEntity]), AuthenticationModule, UsersModule],
  controllers: [GroupsController],
  providers: [GroupsService],
  exports: [GroupsService],
})
export class IPGroupsModule {}
