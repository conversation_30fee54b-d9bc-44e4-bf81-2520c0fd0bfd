import { HttpException, HttpStatus, Injectable, NotFoundException } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { In, IsNull, Like, Not, Repository } from "typeorm";
import { CreateIpDto } from "./dto/create-ip.dto";
import { UpdateIpDto } from "./dto/update-ip.dto";
import { IpEntity } from "./entities/ip.entity";
import { GroupsService } from "@/modules/group/groups.service";
import { GenericFilter } from "@/common/filters/dtos/generic-filter.dto";
import { IPFilter } from "./interfaces/ips.interface";
import { SortOrder } from "@/common/filters/sortOrder";
import { to } from "@/common/utils/to";
import { getPage, getPageSize, getTotalPage } from "@/common/utils/pagination.utils";

@Injectable()
export class IpsService {
  constructor(
    @InjectRepository(IpEntity)
    private readonly ipRepository: Repository<IpEntity>,
    private readonly groupsService: GroupsService,
  ) { }

  async create(createIpDto: CreateIpDto): Promise<IpEntity> {
    const group = await this.groupsService.findOne(createIpDto.ip_groups_id);
    const ip = this.ipRepository.create({
      ip: createIpDto.ip,
      is_activated: true,
      group,
    });
    return await this.ipRepository.save(ip);
  }

  async findAll(filter: GenericFilter & IPFilter): Promise<{
    data: Array<IpEntity>;
    totalItems: number;
    totalPages: number;
    currentPage: number;
    pageSize: number;
  }> {
    if (!filter.sortOrder) {
      filter.sortOrder = SortOrder.ASC;
    }

    if (!filter.name) {
      filter.name = ''
    }

    if (!filter.ip) {
      filter.ip = ''
    }

    if (!filter.is_activated) {
      filter.is_activated = null
    }

    const customers = filter.customers ? filter.customers.split(',') : [''];
    const page = getPage(filter?.page);
    const pageSize = getPageSize(filter?.pageSize);
    const totalItems = await this.ipRepository.count({
      where: {
        is_activated: filter.is_activated,
        ip: Like(`%${filter.ip}%`),
        group: {
          name: Like(`%${filter.name}%`),
          customers_id: In(customers)
        }

      },
    });
    const totalPages = getTotalPage(totalItems, pageSize);

    const [data, error] = await to<IpEntity[], Error>(
      this.ipRepository.find({
        relations: {
          group: true
        },
        skip: (page - 1) * pageSize,
        take: pageSize,
        where: {
          is_activated: filter.is_activated,
          ip: Like(`%${filter.ip}%`),
          group: {
            name: Like(`%${filter.name}%`),
            customers_id: In(customers)
          },
        },
        order: { group: { name: filter.sortOrder }, },
      }),
    );

    if (error)
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);

    return {
      data: data,
      totalItems,
      totalPages,
      currentPage: page,
      pageSize,
    };
  }

  async findOne(id: string): Promise<IpEntity> {
    const ip = await this.ipRepository.findOne({
      where: { id },
      relations: ["group"],
    });
    if (!ip) {
      throw new NotFoundException("IP not found");
    }
    return ip;
  }

  async update(id: string, updateIpDto: UpdateIpDto): Promise<IpEntity> {
    const ip = await this.findOne(id);
    if (updateIpDto.ip) {
      ip.ip = updateIpDto.ip;
    }

    if (updateIpDto.is_activated !== undefined) {
      ip.is_activated = updateIpDto.is_activated ? 1 : 0
    }

    if (updateIpDto.ip_groups_id) {
      const group = await this.groupsService.findOne(updateIpDto.ip_groups_id);
      ip.group = group;
    }
    return await this.ipRepository.save(ip);
  }

  async remove(id: string): Promise<void> {
    const result = await this.ipRepository.softDelete(id);
    if (result.affected === 0) {
      throw new NotFoundException("IP not found");
    }
  }

  async findDeleted(): Promise<IpEntity[]> {
    return await this.ipRepository.find({
      where: { deletedAt: Not(IsNull()) },
      withDeleted: true,
      relations: ["group"],
    });
  }
}
