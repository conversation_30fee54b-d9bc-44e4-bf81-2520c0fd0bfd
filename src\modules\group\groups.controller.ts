import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Req,
  HttpCode,
} from "@nestjs/common";
import { GroupsService } from "./groups.service";
import { CreateGroupDto } from "./dto/create-group.dto";
import { UpdateGroupDto } from "./dto/update-group.dto";
import { IpGroupEntity } from "./entities/group.entity";
import { Jwt2faAuthGuard } from "../../common/guards/jwt-2fa/jwt-2fa-auth.guard";
import { IpGroupsGuard } from "@/common/guards/rules/ip-gropus.guard";
import { PermissionsGuard } from "@/common/guards/rules/permission.guard";
import { ApiBearerAuth, ApiHeader, ApiResponse, ApiTags } from "@nestjs/swagger";
import { FilterGroupDto } from "./dto/filter-group.dto";

@ApiTags("Groups")
@ApiBearerAuth("access-token")
@Controller("v1/pam/groups")
@UseGuards(PermissionsGuard)
@UseGuards(IpGroupsGuard)
export class GroupsController {
  constructor(private readonly groupsService: GroupsService) {}

  @Post()
  @UseGuards(Jwt2faAuthGuard)
  @HttpCode(204)
  @ApiResponse({ status: 204, description: 'Success.' })  
  @ApiHeader({ name: "partner-id", required: true })
  async create(@Body() createGroupDto: CreateGroupDto,@Req() req): Promise<IpGroupEntity[]> {
    return this.groupsService.create(createGroupDto,req);
  }

  @Get('?')
  @UseGuards(Jwt2faAuthGuard)
  @HttpCode(200)
  @ApiResponse({ status: 200, description: 'Success.', type: [IpGroupEntity] })
  async findAll(
    @Query() genericFilter: FilterGroupDto,
  ): Promise<{
    data: Array<IpGroupEntity>;
    totalItems: number;
    totalPages: number;
    currentPage: number;
    pageSize: number;
  }> {
    return await this.groupsService.findAll(genericFilter);
  }

  @Get(":id")
  @UseGuards(Jwt2faAuthGuard)
  @HttpCode(200)  
  @ApiResponse({ status: 200, description: 'Success.', type: IpGroupEntity })
  async findOne(
    @Param("id") id: string
  ): Promise<IpGroupEntity> {
    return await this.groupsService.findOne(id);
  }

  @Patch(":id")
  @UseGuards(Jwt2faAuthGuard)
  @HttpCode(200)
  @ApiResponse({ status: 200, description: 'Success.', type: IpGroupEntity })
  update(@Param("id") id: string, @Body() updateGroupDto: UpdateGroupDto,@Req() req): Promise<IpGroupEntity> {
    return this.groupsService.update(id, updateGroupDto,req);
  }

  @Delete(":id")
  @UseGuards(Jwt2faAuthGuard)
  @HttpCode(200)
  @ApiResponse({ status: 200, description: 'Success.' })
  remove(@Param("id") id: string) {
    return this.groupsService.remove(id);
  }
}
function ApiBearer(): (target: typeof GroupsController) => void | typeof GroupsController {
  throw new Error("Function not implemented.");
}

