import {
  IsA<PERSON>y,
  IsBoolean,
  IsDate,
  IsEmail,
  IsN<PERSON>ber,
  IsOptional,
  IsString,
  Matches,
} from "class-validator";
import { UpdateGroupDto } from "@/modules/permissions/dto/update-permission-groups.dto";
import { CustomerDto } from "../../customers/dto/customer.dto";
import { Type } from "class-transformer";
export class UserDto {
  @IsString()
  @IsOptional()
  id?: string;

  @IsEmail()
  email: string;

  @Matches(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[$&+,:;=?@#|'<>.^*()%!-])[A-Za-z\d@$&+,:;=?@#|'<>.^*()%!-]{8,}$/,
    { message: "Senha inválida" }
  )
  password: string;

  @IsString()
  userName: string;

  @IsString()
  name: string;

  @IsString()
  surname: string;

  @IsString()
  address: string;

  @IsString()
  phoneNumber: string;

  @IsBoolean()
  isSuspended: boolean;

  @IsString()
  twoFactorAuthenticationSecret: string;

  @IsBoolean()
  mustChangePassword: boolean;

  @IsBoolean()
  isTwoFactorAuthenticationEnabled: boolean;

  @IsArray()
  customers: CustomerDto[];

  @IsString()
  @IsOptional()
  country?: string;

  //remove state after
  @IsString()
  @IsOptional()
  state?: string;

  @IsDate()
  @Type(() => Date)
  @IsOptional()
  createdAt: Date;

  @IsDate()
  @IsOptional()
  updatedAt?: Date;

  @IsDate()
  @IsOptional()
  deletedAt?: Date;

  @IsDate()
  @IsOptional()
  lastLoginAt?: Date;

  @IsArray()
  @IsOptional()
  permissionGroups?: UpdateGroupDto[];

  @IsString()
  @IsOptional()
  resetCode?: string;
  
  @IsBoolean()
  @IsOptional()
  isResetCode?: boolean;
  
  @IsBoolean()
  @IsOptional()
  isDev?: boolean;

  @IsString()
  @IsOptional()
  resetExpires?: Date;

  @IsBoolean()
  @IsOptional()
  isEmailConfirmed?: boolean;

  @IsNumber()
  @IsOptional()
  codeEmailConfirmed?: number;

  @IsBoolean()
  @IsOptional()
  isMaster?: boolean;

  @IsDate()
  @IsOptional()
  lastPasswordUpdate?: Date;
}
