import { Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { IpEntity } from "./entities/ip.entity";
import { IpsController } from "./ips.controller";
import { IpsService } from "./ips.service";
import { IpGroupEntity } from "@/modules/group/entities/group.entity";
import { IPGroupsModule } from "@/modules/group/ip-groups.module";
import { AuthenticationModule } from "../authentication/authentication.module";
import { UsersModule } from "../users/users.module";

@Module({
  imports: [
    TypeOrmModule.forFeature([IpEntity, IpGroupEntity]),
    IPGroupsModule,
    AuthenticationModule,
    UsersModule
  ],
  controllers: [IpsController],
  providers: [IpsService],
})
export class IPModule {}
