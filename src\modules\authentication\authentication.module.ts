import { Module } from "@nestjs/common";
import { AuthenticationService } from "@/modules/authentication/authentication.service";
import { AuthenticationController } from "@/modules/authentication/authentication.controller";
import { LocalStrategy } from "@/common/guards/local/local.strategy";
import { UsersModule } from "@/modules/users/users.module";
import { JwtModule } from "@nestjs/jwt";
import { JwtStrategy } from "@/common/guards/jwt/jwt.strategy";
import { Jwt2faStrategy } from "@/common/guards/jwt-2fa/jwt-2fa.strategy";
import "dotenv/config";
import { LogsService } from "../logHistory/logs.service";
import { PermissionsService } from "../permissions/permissions.service";
import { UserSessionsModule } from "../user-sessions/user-sessions.module";
import { PartnerSettingsModule } from "../partner-settings/partner-settings.module";
import { MailModule } from "../mail/mail.module";
import { UsersLoginHistoricModule } from "../users-login-historic/users-login-historic.module";
import { DateUtilsService } from "@/common/utils/date.utils";

@Module({
  imports: [
    UsersModule,
    JwtModule.register({
      secret: process.env.JWT_SECRET,
      signOptions: { expiresIn: '1d' },
    }),
    UserSessionsModule,
    PartnerSettingsModule,
    MailModule,
    UsersLoginHistoricModule,
    PartnerSettingsModule,
  ],
  controllers: [AuthenticationController],
  providers: [
    AuthenticationService,
    LocalStrategy,
    JwtStrategy,
    Jwt2faStrategy,
    LogsService,
    PermissionsService,
    DateUtilsService,
  ],
  exports: [AuthenticationService],
})
export class AuthenticationModule {}
