import {
    Controller,
    Get,
    Query,
    UseGuards,
    HttpCode,
} from "@nestjs/common";
import { Jwt2faAuthGuard } from "../../common/guards/jwt-2fa/jwt-2fa-auth.guard";
import { IpGroupsGuard } from "@/common/guards/rules/ip-gropus.guard";
import { PermissionsGuard } from "@/common/guards/rules/permission.guard";
import { ApiBearerAuth, ApiResponse, ApiTags } from "@nestjs/swagger";
import { IGetUserSessionsPaginated, UserSessionsService } from "./user-sessions.service";
import { UserSessionFilter } from "./dto/user-session.filter";
import { UserSessionEntity } from "./entities/user-session.entity";

@ApiTags("User-Sessions")
@ApiBearerAuth("access-token")
@Controller("v1/pam/user-session")
@UseGuards(PermissionsGuard)
@UseGuards(IpGroupsGuard)
export class UserSessionController {
    constructor(private readonly userSessionService: UserSessionsService) { }

    @Get('')
    @UseGuards(Jwt2faAuthGuard)
    @HttpCode(200)
    @ApiResponse({ status: 200, description: 'Success.', type: [UserSessionEntity] })    
    async getUserSessionsPaginated(
        @Query() filter: UserSessionFilter,
    ): Promise<IGetUserSessionsPaginated> {
        return await this.userSessionService.getUserSessionsPaginated(filter);
    }

}