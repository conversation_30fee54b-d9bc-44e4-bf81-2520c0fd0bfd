import { TestingModule, Test } from "@nestjs/testing";
import { TypeOrmModule } from "@nestjs/typeorm";
import { UserEntity } from "./entities/user.entity";
import { UsersController } from "./users.controller";
import { UsersService } from "./users.service";
import { InsertResult } from "typeorm";
import { LogsService } from "../logHistory/logs.service";
import { UserDto } from "./dtos/user.dto";

describe('UsersController', () => {
    let controller: UsersController;
    const userMock: Partial<UserDto> = {
        id: '3ce3bcf5-0983-498b-9f5c-4bf4b1391d8d',
        email: 'test',
        name: 'test',
        password: 'test',
        userName: 'test',
        address: 'test',
        country: 'test',
        isSuspended: false,
        lastLoginAt: new Date(),
        phoneNumber: 'test',
        state: 'test',
        surname: 'test',
        twoFactorAuthenticationSecret: 'test',
        createdAt: new Date(),
        updatedAt: new Date(),
        permissions: [],
        isTwoFactorAuthenticationEnabled: false
    }
    const userServiceMock = {
        provide: UsersService,
        useValue:{
            findAll: jest.fn().mockResolvedValue({data:[userMock], totalItems: 1, totalPages: 1, currentPage: 1, pageSize: 1}),
            // findOneUser: jest.fn().mockResolvedValue(userMock),
            createUser: jest.fn().mockResolvedValue(InsertResult),
            updateUser: jest.fn().mockResolvedValue(userMock),
            deleteUser: jest.fn().mockResolvedValue(userMock),
        }
    }
    beforeAll(async () => {
        const module: TestingModule = await Test.createTestingModule({
          controllers: [UsersController],
          imports: [
            TypeOrmModule.forFeature([UserEntity]),
            TypeOrmModule.forRoot({
              type: 'postgres',
              host: 'localhost',
              port: 5432,
              username: 'postgres',
              password: 'postgres',
              database: 'postgres',
              entities: [__dirname + '../../**/*.entity{.ts,.js}'],,
            }),
          ],
          providers: [userServiceMock, LogsService],
        }).compile();
    
        controller = module.get<UsersController>(UsersController);
      });

    it('should get user', async () => {
        const result = await controller.findAll( { pageSize: 1, page: 1} );
        expect(result).toEqual({data: [userMock], totalItems: 1, totalPages: 1, currentPage: 1, pageSize: 1});
    });
    it('should create user', async () => {
        const result = await controller.register(userMock);
        expect(result).toEqual(InsertResult);
    })  
    it('should update user', async () => {
        const result = await controller.updateProfile(userMock.id, userMock, '123.322.122.10');
        expect(result).toEqual(InsertResult);
    })
})