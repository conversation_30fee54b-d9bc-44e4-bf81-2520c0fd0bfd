import { UserEntity } from "@/modules/users/entities/user.entity";
import {
  DeleteDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from "typeorm";
import { CustomerEntity } from "./customer.entity";
import { UsersCustomersHasPermissionsEntity } from './../../permissions/entities/users-customers-has-permission.entity';
import { UsersCustomersHasIpGroupsEntity } from "./users-customers-has-ip-groups.entity";

@Entity("users_customers")
export class CustomerUserEntity {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @ManyToOne(() => UserEntity, (user) => user.customers, {eager:false})
  @JoinColumn({ name: "users_id" })
  user: UserEntity;

  @ManyToOne(() => CustomerEntity, (customer) => customer.users, {eager:false})
  @JoinColumn({ name: "customers_id" })
  customers: CustomerEntity;

  @OneToMany(() => UsersCustomersHasPermissionsEntity, 
  (userCustomer) => userCustomer.customerUser, {eager:false})
  UsersCustomersHasPermissionsEntity: UsersCustomersHasPermissionsEntity[];
  
  @OneToMany(() => UsersCustomersHasIpGroupsEntity,
    (customerUser) => customerUser.customerUser, { eager: false })
  usersCustomersHasIpGroups: UsersCustomersHasIpGroupsEntity[];

  @DeleteDateColumn()
  deletedAt?: Date;
}
