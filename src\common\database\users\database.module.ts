import { Module } from "@nestjs/common";
import { databaseProviders } from "@/common/database/users/database.providers";
import { TypeOrmModule } from "@nestjs/typeorm";
import { UserEntity } from "@/modules/users/entities/user.entity";
import { PermissionsEntity } from "@/modules/permissions/entities/permissions.entity";
import { GroupEntity } from "@/modules/permissions/entities/permission-groups.entity";
import { IpEntity } from "@/modules/ips/entities/ip.entity";
import { IpGroupEntity } from "@/modules/group/entities/group.entity";
import { CustomerEntity } from "@/modules/customers/entities/customer.entity";
import { LogEntity } from "@/modules/logHistory/entities/log.entity";
import { CustomerUserEntity } from "@/modules/customers/entities/customer-user.entity";
import { UsersCustomersHasPermissionsEntity } from "@/modules/permissions/entities/users-customers-has-permission.entity";
import { UsersPermissionGroupsEntity } from "@/modules/permissions/entities/users_permission_groups.entity";
import { UsersCustomersHasIpGroupsEntity } from "@/modules/customers/entities/users-customers-has-ip-groups.entity";
import { UserSessionEntity } from "@/modules/user-sessions/entities/user-session.entity";
import { UsersLoginHistoricEntity } from "@/modules/users-login-historic/entities/users-login-historic.entity";
import { CategoryPermissionEntity } from "@/modules/permissions/entities/category_permission";

@Module({
  imports: [
    TypeOrmModule.forRoot({
      type: process.env.DATABASE_TYPE as any,
      host: process.env.DATABASE_HOST,
      port: +process.env.DATABASE_PORT,
      username: process.env.DATABASE_USER,
      password: process.env.DATABASE_PASSWORD,
      database: process.env.DATABASE_DB,
      entities: [
        UserEntity,
        PermissionsEntity,
        GroupEntity,
        CategoryPermissionEntity,
        IpEntity,
        IpGroupEntity,
        CustomerEntity,
        LogEntity,
        CustomerUserEntity,
        UsersCustomersHasPermissionsEntity,
        UsersPermissionGroupsEntity,
        UsersCustomersHasIpGroupsEntity,
        UserSessionEntity,
        UsersLoginHistoricEntity,
      ],
      // logging:'all',
      synchronize: false,
    }),
  ],
  providers: [...databaseProviders],
  exports: [...databaseProviders],
})
export class DatabaseModule {}
