import { IsInt, IsOptional, IsString, Min } from "class-validator";

export class SecuritySettingsDto {
  @IsOptional()
  @IsString()
  id?: string;

  @IsString()
  idPartner?: string;

  @IsOptional()
  @IsInt()
  @Min(1)
  passwordExpirationDays?: number;

  @IsOptional()
  @IsInt()
  @Min(1)
  temporaryPasswordExpireDays?: number;

  @IsOptional()
  @IsInt()
  @Min(1)
  minPasswordLength?: number;

  @IsOptional()
  @IsString()
  passwordRegex?: string;

  @IsOptional()
  @IsInt()
  @Min(1)
  passwordAttemptLimit?: number;

  @IsOptional()
  @IsInt()
  @Min(0)
  passwordExpirationNoticeDays?: number;

  @IsOptional()
  @IsInt()
  @Min(0)
  loginBlockMinutes?: number;

  @IsOptional()
  createdAt?: string;

  @IsOptional()
  updatedAt?: string;
}
