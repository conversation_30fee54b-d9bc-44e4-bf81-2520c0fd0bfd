import { GenericFilter } from "@/common/filters/dtos/generic-filter.dto";
import { UserFilter } from "@/common/filters/interface/userFilter.interface";
import {
  Controller,
  UseGuards,
  Get,
  Param,
  Post,
  HttpCode,
  UsePipes,
  ValidationPipe,
  Body,
  Patch,
  Ip,
  Req,
  Query,
  HttpException,
  HttpStatus,
  Delete,
  ForbiddenException,
} from "@nestjs/common";
import { Jwt2faAuthGuard } from "../../common/guards/jwt-2fa/jwt-2fa-auth.guard";
import { LogsService } from "../logHistory/logs.service";
import { ChangePasswordDto } from "./dtos/change-password.dto";
import { UpdateUserDto } from "./dtos/update-user.dto";
import { UserDto } from "./dtos/user.dto";
import { UsersService } from "./users.service";
import { ApiBearerAuth, ApiHeader, ApiResponse, ApiTags } from "@nestjs/swagger";
import { CreateGroupDto } from "../group/dto/create-group.dto";
import { MailService } from "../mail/mail.service";
import { ChangeEmailPasswordDto } from "./dtos/change-email-password.dto";
import { IpGroupsGuard } from "@/common/guards/rules/ip-gropus.guard";
import { PermissionsGuard } from "@/common/guards/rules/permission.guard";
import { CheckUserPermissionDto } from "./dtos/check-user-permission.dto";
import { AuthenticationService } from "../authentication/authentication.service";

@ApiTags('User')
@ApiBearerAuth('access-token')
@ApiHeader({
  name: 'partner-id',
  description: 'partner id',
  required: true,
})
@ApiHeader({
  name: 'client-ip',
  description: 'client ip',
  required: true,
})
@Controller('v1/pam/users')
export class UsersController {
  constructor(
    private readonly usersService: UsersService,
    private readonly logsService: LogsService,
    private readonly mailService: MailService,
    private authenticationService: AuthenticationService,
  ) {}

  @Get('profile')
  @UseGuards(Jwt2faAuthGuard)
  @UseGuards(IpGroupsGuard)
  @ApiResponse({ status: 200, description: 'Success.', type: UserDto })
  async findProfile(@Req() req: any): Promise<Partial<UserDto>> {
    return await this.usersService.findOneById(req.user.id);
  }

  @Get('didEdit')
  @UseGuards(Jwt2faAuthGuard)
  @UseGuards(PermissionsGuard)
  @UseGuards(IpGroupsGuard)
  @HttpCode(200)
  @ApiResponse({ status: 200, description: 'Success.', type: [UserDto] })
  async findDidEdit(): Promise<UserDto[]> {
    return await this.usersService.findDidEdit();
  }

  @Get('/email/:email')
  @UseGuards(Jwt2faAuthGuard)
  @UseGuards(PermissionsGuard)
  @UseGuards(IpGroupsGuard)
  @HttpCode(200)
  @ApiResponse({ status: 200, description: 'Success.', type: UserDto })
  async findOneUser(@Param("email") email: string, @Req() req): Promise<Partial<UserDto> | undefined> {
    return await this.usersService.findOneUser(email);
  }

  @Get(':id')
  @UseGuards(Jwt2faAuthGuard)
  @UseGuards(PermissionsGuard)
  @UseGuards(IpGroupsGuard)
  @HttpCode(200)
  @ApiResponse({ status: 200, description: 'Success.', type: UserDto })
  @ApiResponse({ status: 204, description: 'User not found.' })
  async findOneById(
    @Param('id') id: string,
    @Req() req,
  ): Promise<Partial<UserDto> | undefined> {
    console.log('teste');
    const user = await this.usersService.findOneById(id);
    return user;
  }

  @Post()
  @UseGuards(Jwt2faAuthGuard)
  @UsePipes(ValidationPipe)
  @UseGuards(PermissionsGuard)
  @UseGuards(IpGroupsGuard)
  @HttpCode(201)
  @ApiResponse({ status: 201, description: 'The record has been successfully created.' })
  async register(@Body() body: UserDto, @Req() req) {
    return await this.usersService.createUser(body, req);
  }

  @Patch(':id')
  @UseGuards(Jwt2faAuthGuard)
  @UsePipes(ValidationPipe)
  @UseGuards(PermissionsGuard)
  @UseGuards(IpGroupsGuard)
  @HttpCode(200)
  @ApiResponse({ status: 200, description: 'The record has been successfully updated.' })
  async updateProfile(
    @Param('id') id: string,
    @Body() updateUserDto: UpdateUserDto,
    @Ip() ip: string,
    @Req() req,
  ) {
    const log = await this.usersService.updateUser(updateUserDto, id, ip, req);
    return log;
  }
  @Patch(':id/resetTwoFactor')
  @UseGuards(Jwt2faAuthGuard)
  @UsePipes(ValidationPipe)
  @UseGuards(PermissionsGuard)
  @UseGuards(IpGroupsGuard)
  @HttpCode(200)
  @ApiResponse({ status: 200, description: 'The record has been successfully updated.' })
  async resetTwoFactor(
    @Param('id') id: string,
    @Ip() ip: string,
    @Req() req,
  ) {
    const log = await this.usersService.resetTwoFactor(id, ip, req);
    return log;
  }

  @UseGuards(Jwt2faAuthGuard)
  @UseGuards(PermissionsGuard)
  @UseGuards(IpGroupsGuard)
  @Get('?')
  @HttpCode(200)
  @ApiResponse({ status: 200, description: 'Success.', type: [UserDto] })  
  async findAll(
    @Query() genericFilter: GenericFilter & UserFilter,
    @Req() req,
  ): Promise<{
    data: UserDto[];
    totalItems: number;
    totalPages: number;
    currentPage: number;
    pageSize: number;
  }> {
    return await this.usersService.findAll(
      genericFilter,
      req.user.id,
      req.user.customers,
    );
  }

  @Patch('change-password/:id')
  @UseGuards(Jwt2faAuthGuard)
  @UsePipes(ValidationPipe)
  @UseGuards(PermissionsGuard)
  @UseGuards(IpGroupsGuard)
  @HttpCode(200)
  @ApiResponse({ status: 200, description: 'The record has been successfully updated.' })
  async changePassword(
    @Param('id') id: string,
    @Body() changePass: ChangePasswordDto,
    @Ip() ip: string,
    @Req() req,
  ): Promise<{ id: string }> {
    try {
      if (changePass.password !== changePass.confirmPassword)
        throw new HttpException(
          'Passwords do not match',
          HttpStatus.BAD_REQUEST,
        );

      if (req.user.id === id) {
        await this.usersService.changePasswordMySelf(
          id,
          ip,
          req.user,
          changePass,
        );

        return { id: id };
      }

      const userDto = new UpdateUserDto();
      userDto.password = changePass.password;

      await this.usersService.changePassword(userDto, id, ip, req.user.id);
      return { id: id };
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  @Post('must-change/password')
  @HttpCode(200)
  @ApiResponse({
    status: 200,
    description: 'The record has been successfully updated.',
  })
  async mustChangePassword(
    @Body() changePass: ChangePasswordDto,
    @Ip() ip: string,
    @Req() req,
  ): Promise<{ id: string }> {
    const token = req.headers.authorization;
    const { email } = this.authenticationService.validateToken(token);
    const user = await this.usersService.findOneUser(email);

    if (changePass.password !== changePass.confirmPassword) {
      throw new HttpException('Passwords do not match', HttpStatus.BAD_REQUEST);
    }

    await this.usersService.changePasswordFirstAccess(
      user.id,
      ip,
      user,
      changePass,
    );

    return { id: user.id };
  }

  @Patch('change-password-first-access/:id')
  @UseGuards(Jwt2faAuthGuard)
  @UsePipes(ValidationPipe)
  @UseGuards(PermissionsGuard)
  @UseGuards(IpGroupsGuard)
  @HttpCode(200)
  @ApiResponse({ status: 200, description: 'The record has been successfully updated.' })  
  async changePasswordFirstAccess(
    @Param('id') id: string,
    @Body() changePass: ChangePasswordDto,
    @Ip() ip: string,
    @Req() req,
  ): Promise<{ id: string }> {
    if (changePass.password !== changePass.confirmPassword)
      throw new HttpException('Passwords do not match', HttpStatus.BAD_REQUEST);

    if (req.user.id === id) {
      await this.usersService.changePasswordFirstAccess(
        id,
        ip,
        req.user,
        changePass,
      );

      return { id: id };
    }

    const userDto = new UpdateUserDto();
    userDto.password = changePass.password;

    await this.usersService.changePassword(userDto, id, ip, req.user.id);
    return { id: id };
  }

  @Patch('ip-groups/:id')
  @UseGuards(Jwt2faAuthGuard)
  @UsePipes(ValidationPipe)
  @UseGuards(PermissionsGuard)
  @UseGuards(IpGroupsGuard)
  @HttpCode(200)
  @ApiResponse({ status: 200, description: 'The record has been successfully updated.' })
  async updateIp(
    @Param('id') id: string,
    @Body() groupIp: Partial<CreateGroupDto[]>,
    @Req() req,
  ) {
    return await this.usersService.updateIp(groupIp, id);
  }

  @Delete(':id')
  @UseGuards(Jwt2faAuthGuard)
  @UseGuards(PermissionsGuard)
  @UseGuards(IpGroupsGuard)
  @HttpCode(200)
  @ApiResponse({ status: 200, description: 'The record has been successfully deleted.' })  
  async delete(@Param("id") id: string, @Req() req) {
    if (req.user.id !== id) {
      return await this.usersService.deleteUser(id);
    }

    throw new ForbiddenException();
  }

  @Post('send-email-password')
  @UsePipes(ValidationPipe)
  @HttpCode(201)
  @ApiResponse({ status: 201, description: 'Mail sent.' })  
  async resetPassword(@Body() body: { email: string }, @Req() req) {
    return await this.usersService.resetPassword(body.email, req);
  }

  @Post('reset-password')
  @UsePipes(ValidationPipe)
  @HttpCode(201)
  @ApiResponse({ status: 201, description: 'Password changed successfully.' })  
  async changePasswordEmail(@Body() body: ChangeEmailPasswordDto, @Ip() ip: string) {
    return await this.usersService.changePasswordEmail(body, ip);
  }

  @Post('confirm-email')
  @UsePipes(ValidationPipe)
  @UseGuards(PermissionsGuard)
  @UseGuards(IpGroupsGuard)
  @HttpCode(201)
  @ApiResponse({ status: 201, description: 'Mail sent.' })  
  async confirmEmail(@Body() body: { email: string }) {
    return await this.usersService.confirmEmail(body.email);
  }

  @Post('turn-on-email')
  @UsePipes(ValidationPipe)
  @HttpCode(201)
  @ApiResponse({ status: 201, description: 'Email confirmed successfully.' })  
  async turnOnEmail(@Body() body: { email: string; code: number }) {
    return await this.usersService.turnOnEmailValidation(body.email, body.code);
  }

  @UseGuards(Jwt2faAuthGuard)
  @Get('/:userId/check-permission')
  @HttpCode(200)
  @ApiResponse({ status: 200, description: 'Success.', type: Boolean })  
  async checkPermission(
    @Param('userId') userId: string,
    @Query() { method, path, customerPartnerId }: CheckUserPermissionDto,
  ): Promise<boolean> {
    return await this.usersService.checkPermissions(
      userId,
      path,
      method,
      customerPartnerId,
    );
  }
}
