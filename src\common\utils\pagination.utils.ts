export function getSkip(skip: number, totalItems: number): number {
    return skip >= totalItems ? 0 : skip
}

export function getTake(pageSize: number, totalItems: number, skip: number): number {
    return Math.min(pageSize, totalItems - skip)
}

export function getPage(page?: number): number {
    return !page || page < 1 ? 1 : page;
}

export function getPageSize(pageSize?: number): number {
    return !pageSize || pageSize < 1 ? 10 : pageSize;
}

export function getTotalPage(totalItems: number, pageSize: number): number {
    return Math.ceil(totalItems / pageSize)
}