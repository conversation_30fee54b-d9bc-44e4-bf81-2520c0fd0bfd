import { DataSource } from "typeorm";
import "dotenv/config";

export const databasePlayerProviders = [
  {
    provide: "DATABASE_CONNECTION",
    useFactory: async () => {
      const db = new DataSource({
        type: "postgres",
        host: "localhost",
        port: 5432,
        username: process.env.POSTGRES_USER,
        password: process.env.POSTGRES_PASSWORD,
        database: process.env.POSTGRES_PLAYERS_DB,
        entities: [__dirname + "../../**/*.entity{.ts,.js}"],
        synchronize: true,
      });
      return db.initialize();
    },
  },
];
