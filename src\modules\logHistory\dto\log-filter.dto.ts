import { GenericFilter } from "@/common/filters/dtos/generic-filter.dto";
import { ApiProperty } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsDate, IsOptional, IsString } from "class-validator";

export class LogFilter extends GenericFilter {
    @ApiProperty({ required: false })
    @IsOptional()
    @IsString()
    email: string;

    @ApiProperty({ required: false })
    @IsOptional()
    @IsString()
    userName: string;

    @ApiProperty({ required: false, description: "User ID" })
    @IsOptional()
    @IsString()
    id: string;

    @ApiProperty({ required: false })
    @IsOptional()
    @IsString()
    field: string;

    @ApiProperty({ required: false })
    @IsDate()
    @IsOptional()
    @Transform(({ value }) => {
        const date = new Date(value)
        date.setHours(0, 0, 0, 0)
        return date
    })
    public initialDate?: Date;

    @ApiProperty({ required: false })
    @IsDate()
    @IsOptional()
    @Transform(({ value }) => {
        const date = new Date(value)
        date.setHours(23, 59, 59, 999)
        return date
    })
    public finalDate?: Date;

}