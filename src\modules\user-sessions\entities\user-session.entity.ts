import { UserEntity } from "@/modules/users/entities/user.entity";
import {
  Column,
  <PERSON>tity,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
  UpdateDateColumn,
} from "typeorm";

@Entity("user_sessions")
export class UserSessionEntity {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ type: "varchar", length: 45, name: "user_id" })
  userId: string;

  @Column({ type: "varchar", length: 45, nullable: true })
  ip?: string;

  @Column({ type: "varchar", length: 255, nullable: true })
  location?: string;

  @Column({ type: "varchar", length: 255, nullable: true })
  device?: string;

  @CreateDateColumn({ default: 'NOW()', name: "created_at" })
  createdAt: Date;

  @UpdateDateColumn({ name: "updated_at" })
  updatedAt?: Date;

  @ManyToOne(() => UserEntity, (user) => user.userSessions, { eager: false })
  @JoinColumn({ name: "user_id" })
  user: UserEntity;
}
