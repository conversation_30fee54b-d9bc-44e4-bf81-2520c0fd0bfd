import { HttpService } from "@nestjs/axios";
import { Injectable, Logger } from "@nestjs/common";
import { SecuritySettingsDto } from "./dto/security-settings.dto";
import { CustomerDto } from "../customers/dto/customer.dto";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { PartnerSecuritySettings } from "./entities/security-settings.entity";
import { firstValueFrom } from "rxjs";
import { PartnerIpRestrictions } from "./entities/ip-restrictions.entity";

@Injectable()
export class PartnerSettingsService {
  private readonly logger = new Logger(PartnerSettingsService.name);

  constructor(
    private readonly httpService: HttpService,
    @InjectRepository(PartnerSecuritySettings, "partnersConnection")
    private readonly securitySettingsRepo: Repository<PartnerSecuritySettings>,

    @InjectRepository(PartnerIpRestrictions, "partnersConnection")
    private readonly ipRestrictionsRepo: Repository<PartnerIpRestrictions>
  ) { }

  private apiPartnerSettings = `${process.env.PARTNER_API_URL}/v1/pam/utils/partners`;

  private readonly defaultSettings: SecuritySettingsDto = {
    passwordExpirationDays: 90,
    temporaryPasswordExpireDays: 7,
    minPasswordLength: 8,
    passwordRegex: '^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d).{8,}$',
    passwordAttemptLimit: 5,
    passwordExpirationNoticeDays: 5,
    loginBlockMinutes: 15,
  };

  async getIpRestrictionsForPartners(partners: CustomerDto[]): Promise<PartnerIpRestrictions[]> {
    const partnerIds = partners.map((p) => p.id);

    this.logger.log(
      `Buscando restrições de IP para ${partnerIds.length} parceiros`
    );

    const ipRestrictionsList = await this.ipRestrictionsRepo
      .createQueryBuilder("restrictions")
      .where("restrictions.idPartner IN (:...partnerIds)", { partnerIds })
      .getMany();

    return ipRestrictionsList;
  }

  async getSecuritySettings(partners: CustomerDto[]) {
    const partnerIds = partners.map((p) => p.id);

    this.logger.log(
      `Buscando configurações locais de segurança para ${partnerIds.length} parceiros`
    );

    const settingsList = await this.securitySettingsRepo
      .createQueryBuilder('settings')
      .where('settings.idPartner IN (:...partnerIds)', { partnerIds })
      .getMany();

    const validSettings = settingsList.filter((s) => !!s);

    if (!validSettings.length) {
      this.logger.warn(
        'Nenhuma configuração válida encontrada. Usando configuração padrão.'
      );
      return this.defaultSettings;
    }

    const merged = validSettings.reduce((acc, cur) => {
      return {
        ...acc,
        passwordExpirationDays: Math.min(
          acc.passwordExpirationDays,
          cur.passwordExpirationDays
        ),
        passwordAttemptLimit: Math.min(
          acc.passwordAttemptLimit,
          cur.passwordAttemptLimit
        ),
        passwordExpirationNoticeDays: Math.max(
          acc.passwordExpirationNoticeDays,
          cur.passwordExpirationNoticeDays
        ),
        loginBlockMinutes: Math.min(
          acc.loginBlockMinutes,
          cur.loginBlockMinutes
        ),
        minPasswordLength: Math.min(
          acc.minPasswordLength,
          cur.minPasswordLength
        ),
      };
    }, validSettings[0]);

    this.logger.log(
      `Configurações de segurança mescladas: ${JSON.stringify(merged)}`
    );

    return merged;
  }
}
