import { UserEntity } from "@/modules/users/entities/user.entity";
import { Column, Entity, ManyToOne, PrimaryGeneratedColumn } from "typeorm";

@Entity("update_log")
export class LogEntity {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column()
  userId: string;

  @ManyToOne(() => UserEntity, (user) => user.id, { eager: false })
  user: UserEntity;

  @Column()
  updatedAt: Date;

  @Column()
  ip: string;

  @Column()
  field: string;

  @Column()
  oldValue: string;

  @Column()
  newValue: string;

  @Column()
  changed_id: string;

  @Column()
  entity: string;

  @Column()
  userDidId: string;
}
