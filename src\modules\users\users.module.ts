import { Module } from '@nestjs/common';
import { UsersService } from '@/modules/users/users.service';
import { usersProviders } from '@/modules/users/users.provider';
import { DatabaseModule } from '@/common/database/users/database.module';
import { UsersController } from '@/modules/users/users.controller';
import { LogsService } from '@/modules/logHistory/logs.service';
import { updateLogProviders } from '@/modules/logHistory/log.provider';
import { PermissionsService } from '@/modules/permissions/permissions.service';
import { CustomersService } from '../customers/customers.service';
import { MailModule } from '../mail/mail.module';
import { AuthenticationService } from '../authentication/authentication.service';
import { JwtService } from '@nestjs/jwt';
import { UserSessionsService } from '../user-sessions/user-sessions.service';
import { UsersSyncController } from './users-sync.controller';
import { PartnerSettingsModule } from '../partner-settings/partner-settings.module';
import { UsersLoginHistoricModule } from '../users-login-historic/users-login-historic.module';
import { DateUtilsService } from '@/common/utils/date.utils';

@Module({
  imports: [
    DatabaseModule,
    MailModule,
    PartnerSettingsModule,
    UsersLoginHistoricModule,
  ],
  providers: [
    ...usersProviders,
    ...updateLogProviders,
    UsersService,
    LogsService,
    AuthenticationService,
    PermissionsService,
    CustomersService,
    UserSessionsService,
    JwtService,
    DateUtilsService,
  ],
  controllers: [UsersController, UsersSyncController],
  exports: [UsersService, JwtService],
})
export class UsersModule {}
