import { Strategy } from "passport-local";
import { PassportStrategy } from "@nestjs/passport";
import { Injectable, UnauthorizedException } from "@nestjs/common";
import { AuthenticationService } from "@/modules/authentication/authentication.service";
import { User } from "@/modules/users/interface/user.interface";
import { SecuritySettingsDto } from "@/modules/partner-settings/dto/security-settings.dto";
import { PartnerSecuritySettings } from "@/modules/partner-settings/entities/security-settings.entity";

interface ValidatedUser extends Partial<User> {
  settings: SecuritySettingsDto | PartnerSecuritySettings;
}

@Injectable()
export class LocalStrategy extends PassportStrategy(Strategy) {
  constructor(private authenticationService: AuthenticationService) {
    super({
      usernameField: "email",
      passwordField: "password",
      passReqToCallback: true,
    });
  }

  async validate(
    req: Request,
    email: string,
    password: string
  ): Promise<ValidatedUser> {
    const ip =
      req.headers["client-ip"] ?? req.headers["x-forwarded-for"] ?? "unknown";

    const device = req.headers["user-agent"] ?? "unknown";

    const validatedUser = await this.authenticationService.validateUser(
      email,
      password,
      ip,
      device
    );
    if (!validatedUser) {
      throw new UnauthorizedException();
    }
    return validatedUser;
  }
}
