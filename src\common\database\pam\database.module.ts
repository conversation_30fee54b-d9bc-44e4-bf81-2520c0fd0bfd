import { Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { databaseProviders } from "./database.provider";
import { PartnerSecuritySettings } from "@/modules/partner-settings/entities/security-settings.entity";
import { PartnerIpRestrictions } from "@/modules/partner-settings/entities/ip-restrictions.entity";

@Module({
  imports: [
    TypeOrmModule.forRoot({
      name: "partnersConnection",
      type: process.env.DATABASE_PAM_TYPE as any,
      host: process.env.DATABASE_PAM_HOST,
      port: process.env.DATABASE_PAM_PORT as unknown as number,
      username: process.env.DATABASE_PAM_USER,
      password: process.env.DATABASE_PAM_PASSWORD,
      database: process.env.DATABASE_PAM_DB,
      entities: [PartnerSecuritySettings, PartnerIpRestrictions],
      synchronize: false,
      // logging: ['query'],
      ssl: true,
      extra: {
        ssl: {
          rejectUnauthorized: false,
        },
      },
    }),
  ],
  providers: [...databaseProviders],
  exports: [...databaseProviders],
})
export class DatabasePamModule {}
