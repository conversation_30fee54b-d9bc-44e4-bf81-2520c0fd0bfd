const password = encodeURIComponent(")@uqi7r#");

module.exports = {
  apps: [
    {
      name: "pam-users",
      script: "./src/main.js",
      watch: false,
      env_development: {
        NODE_ENV: "development",
        PORT: 4000,
        DATABASE_HOST: 'h2-dev.cluster-cly8mo402o27.us-east-1.rds.amazonaws.com',
        DATABASE_USER: 'usr_pam',
        DATABASE_PASSWORD: ')@uqi7r#',
        DATABASE_DB: 'pam',
        DATABASE_TYPE: 'mysql',
        DATABASE_PORT: 3306,
        JWT_SECRET: 'f3f8318b9039c6b26826b8fc7032e1b3500d0186f564468da24c9989ac247283'
      },
      env_staging: {
        NODE_ENV: "staging",
        DATABASE_HOST: 'h2-dev.cluster-cly8mo402o27.us-east-1.rds.amazonaws.com',
        DATABASE_USER: 'usr_pam',
        DATABASE_PASSWORD: ')@uqi7r#',
        DATABASE_DB: 'pam',
        DATABASE_TYPE: 'mysql',
        DATABASE_PORT: 3306,
        JWT_SECRET: 'f3f8318b9039c6b26826b8fc7032e1b3500d0186f564468da24c9989ac247283'
      },
    },
  ],
};
