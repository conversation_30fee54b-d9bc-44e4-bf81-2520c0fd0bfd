import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  ManyToMany,
  JoinTable,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  OneToMany,
} from "typeorm";
import { PermissionsEntity } from "@/modules/permissions/entities/permissions.entity";
import { GroupEntity } from "@/modules/permissions/entities/permission-groups.entity";
import { CustomerEntity } from "@/modules/customers/entities/customer.entity";
import { UserSessionEntity } from "@/modules/user-sessions/entities/user-session.entity";

@Entity("users")
export class UserEntity {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ length: 500 })
  email: string;

  @Column({ length: 500 })
  password: string;

  @Column({ length: 500 })
  userName: string;

  @Column({ length: 500 })
  name: string;

  @Column({ length: 500 })
  surname: string;

  @Column({ length: 50, nullable: true })
  country?: string;

  @Column({ length: 50, nullable: true })
  state?: string;

  @Column({ length: 500, nullable: true })
  address: string;

  @Column({ length: 500, nullable: true })
  phoneNumber: string;

  @Column({ length: 500, nullable: true })
  twoFactorAuthenticationSecret: string;

  @Column({ default: false })
  isTwoFactorAuthenticationEnabled: boolean;

  @CreateDateColumn({ default: "NOW()" })
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt?: Date;

  @DeleteDateColumn({ nullable: true })
  deletedAt?: Date;

  @Column({ nullable: true })
  lastLoginAt?: Date;

  @ManyToMany(() => PermissionsEntity, (permission) => permission.id, {
    eager: false,
    onUpdate: "CASCADE",
    orphanedRowAction: "delete",
    cascade: true,
  })
  @ManyToMany(() => GroupEntity, (group) => group.id, {
    eager: false,
  })
  @JoinTable({
    name: "users_permission_groups",
    joinColumn: {
      name: "users_id",
      referencedColumnName: "id",
    },
    inverseJoinColumn: {
      name: "permission_groups_id",
      referencedColumnName: "id",
    },
  })
  permissionGroups?: GroupEntity[];

  @Column({ default: false })
  isSuspended: boolean;

  @Column({ default: false })
  mustChangePassword: boolean;

  @ManyToMany(() => CustomerEntity, (customer) => customer.id, {
    eager: false,
  })
  @JoinTable({
    name: "users_customers",
    joinColumn: {
      name: "users_id",
      referencedColumnName: "id",
    },
    inverseJoinColumn: {
      name: "customers_id",
      referencedColumnName: "id",
    },
  })
  customers: CustomerEntity[];

  @Column()
  resetCode?: string;
  
  @Column()
  isResetCode?: boolean;

  @Column()
  isDev?: boolean;

  @Column()
  resetExpires?: Date;

  @Column({ name: "confirmed_email", default: false })
  isEmailConfirmed?: boolean;

  @Column({ name: "code_email_confirmed" })
  codeEmailConfirmed: number;

  @Column({
    name: "isGod",
    nullable: true,
    default: false,
    insert: false,
    update: false,
  })
  isMaster?: boolean;

  @OneToMany(() => UserSessionEntity, (userSession) => userSession.user, {
    eager: false,
  })
  userSessions: UserSessionEntity[];

  @Column({ nullable: true })
  lastPasswordUpdate?: Date;
}
