import { Controller, Get, UseGuards, Query, HttpCode } from "@nestjs/common";
import { LogsService } from "@/modules/logHistory/logs.service";
import { Jwt2faAuthGuard } from "@/common/guards/jwt-2fa/jwt-2fa-auth.guard";
import { ApiBearerAuth, ApiResponse, ApiTags } from "@nestjs/swagger";
import { PermissionsGuard } from "@/common/guards/rules/permission.guard";
import { IpGroupsGuard } from "@/common/guards/rules/ip-gropus.guard";
import { LogFilter } from "./dto/log-filter.dto";
import { LogEntity } from "./entities/log.entity";

@ApiTags("Log")
@ApiBearerAuth("access-token")
@Controller("v1/pam/logs")
@UseGuards(PermissionsGuard)
@UseGuards(IpGroupsGuard)
export class LogsController {
  constructor(private readonly logsService: LogsService) {}

  @Get("?")
  @UseGuards(Jwt2faAuthGuard)
  @HttpCode(200)
  @ApiResponse({ status: 200, description: 'Success.', type: [LogEntity] })
  findAll(@Query() filter: LogFilter) {
    return this.logsService.getLogsPaginated(filter);
  }
}
