import { HttpException, HttpStatus, Injectable } from "@nestjs/common";
import { CustomerDto } from "./dto/customer.dto";
import { to } from "@/common/utils/to";
import { CustomerEntity } from "./entities/customer.entity";
import { InjectEntityManager } from "@nestjs/typeorm";
import { EntityManager, UpdateResult } from "typeorm";
import { CustomerUserDto } from "./dto/customer-user.dto";
import { CustomerUserEntity } from "./entities/customer-user.entity";
import { UserDto } from "../users/dtos/user.dto";

@Injectable()
export class CustomersService {
  constructor(@InjectEntityManager() private readonly manager: EntityManager) {}
  async createCustomer(customer: CustomerDto): Promise<CustomerDto> {
    const [inserted, error] = await to<CustomerDto, Error>(
      this.manager.save(CustomerEntity, customer),
    );
    if (error)
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    return inserted;
  }

  async createCustomerUser(
    customer: CustomerUserDto,
    req: any,
    user: UserDto,
  ) {
    return await this.manager.transaction(async (entityManager) => {
      customer.customers.map((customerMap) => {
        const exists = req.user.customers.some(async (userCustomer) => {
          
          const [verifyCustomerUser, errorCustomerUser] = await to<
            CustomerUserEntity[],
            Error
          >(
            this.manager.find(CustomerUserEntity, {
              where: {
                user: { id: user.id },
                customers: { id: customerMap.id },
              },
            }),
          );
          if (verifyCustomerUser.length > 0)
            throw new HttpException(
              "User already has this customer",
              HttpStatus.CONFLICT,
            );
          if (errorCustomerUser)
            throw new HttpException(
              errorCustomerUser.message,
              HttpStatus.INTERNAL_SERVER_ERROR,
            );
          return userCustomer.id === customerMap.id;
        });
        if (!exists)
          throw new HttpException("Not same customer", HttpStatus.NOT_FOUND);
      });
      for (let i = 0; i < customer.customers.length; i++) {
        const [findCustomer, errorCustomer] = await to<CustomerEntity, Error>(
          entityManager.findOneBy(CustomerEntity, {
            id: customer.customers[i].id,
          }),
        );
        const createdCustomer = entityManager.create(CustomerUserEntity, {
          user: user,
          customers: findCustomer,
        });
        this.manager.save(CustomerUserEntity, createdCustomer);
        if (errorCustomer)
          throw new HttpException(
            errorCustomer.message,
            HttpStatus.INTERNAL_SERVER_ERROR,
          );
      }
    });
  }
  async findAll(): Promise<CustomerDto[]> {
    const [customers, error] = await to<CustomerDto[], Error>(
      this.manager.find(CustomerEntity, {
        relations: {
          ipGroups: true,
        },
      }),
    );
    if (error)
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    return customers;
  }

  async findOneById(id: string): Promise<CustomerDto> {
    const [customer, error] = await to<CustomerDto, Error>(
      this.manager.findOneBy(CustomerEntity, { id }),
    );
    if (error)
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    return customer;
  }

  async remove(id: string): Promise<string> {
    const [deleted, error] = await to<UpdateResult, Error>(
      this.manager.softDelete(CustomerUserEntity, { id }),
    );
    if (error)
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    return deleted.generatedMaps[0].id;
  }
}
