import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpCode,
  UseGuards,
  Query,
} from "@nestjs/common";
import { IpsService } from "./ips.service";
import { CreateIpDto } from "./dto/create-ip.dto";
import { UpdateIpDto } from "./dto/update-ip.dto";
import { Jwt2faAuthGuard } from "../../common/guards/jwt-2fa/jwt-2fa-auth.guard";
import { IpEntity } from "./entities/ip.entity";
import { GenericFilter } from "@/common/filters/dtos/generic-filter.dto";
import { IPFilter } from "./interfaces/ips.interface";
import { IpGroupsGuard } from "@/common/guards/rules/ip-gropus.guard";
import { PermissionsGuard } from "@/common/guards/rules/permission.guard";
import { ApiTags, ApiBearerAuth, ApiResponse } from "@nestjs/swagger";


@ApiTags("Ips")
@ApiBearerAuth("access-token")
@Controller("v1/pam/ips")
@UseGuards(PermissionsGuard)
@UseGuards(IpGroupsGuard)
export class IpsController {
  constructor(private readonly ipsService: IpsService) {}

  @Post()
  @UseGuards(Jwt2faAuthGuard)
  @HttpCode(201)
  @ApiResponse({ status: 201, description: 'Success.' })
  create(@Body() createIpDto: CreateIpDto) {
    return this.ipsService.create(createIpDto);
  }

  @Get('?')
  @UseGuards(Jwt2faAuthGuard)
  @HttpCode(200)
  @ApiResponse({ status: 200, description: 'Success.', type: [IpEntity] })
  findAll(
    @Query() genericFilter: GenericFilter & IPFilter,
  ): Promise<{
    data: IpEntity[];
    totalItems: number;
    totalPages: number;
    currentPage: number;
    pageSize: number;
  }> {
    return this.ipsService.findAll(genericFilter);
  }

  @Get("/deleted")
  @UseGuards(Jwt2faAuthGuard)
  @HttpCode(200)
  @ApiResponse({ status: 200, description: 'Success.', type: [IpEntity] })
  findDeleted():Promise<IpEntity[]> {
    return this.ipsService.findDeleted();
  }

  @Get(":id")
  @UseGuards(Jwt2faAuthGuard)
  @HttpCode(200)
  @ApiResponse({ status: 200, description: 'Success.', type: IpEntity })
  findOne(@Param("id") id: string): Promise<IpEntity> {
    return this.ipsService.findOne(id);
  }

  @Patch(":id")
  @UseGuards(Jwt2faAuthGuard)
  @HttpCode(200)
  @ApiResponse({ status: 200, description: 'Success.', type: IpEntity })
  update(@Param("id") id: string, @Body() updateIpDto: UpdateIpDto): Promise<IpEntity> {
    return this.ipsService.update(id, updateIpDto);
  }

  @Delete(":id")
  @UseGuards(Jwt2faAuthGuard)
  @HttpCode(200)
  @ApiResponse({ status: 200, description: 'Success.' })
  remove(@Param("id") id: string) {
    return this.ipsService.remove(id);
  }
}
