import {
  IsString,
  IsOptional,
  IsNotEmpty,
  IsDate,
  IsArray,
  IsObject,
} from 'class-validator';
import { UpdateGroupDto } from '@/modules/permissions/dto/update-permission-groups.dto';
import { UsersCustomersHasPermissionsEntity } from '../entities/users-customers-has-permission.entity';

export class PermissionsDto {
  @IsString()
  @IsNotEmpty()
  id: string;

  @IsOptional()
  @IsString()
  id_category?: string;

  @IsOptional()
  @IsString()
  id_subcategory?: string;

  @IsString()
  @IsNotEmpty()
  method: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsString()
  code?: string;

  @IsOptional()
  @IsString()
  endpoint?: string;

  @IsOptional()
  @IsDate()
  createdAt: Date;

  @IsOptional()
  @IsDate()
  updatedAt: Date;

  @IsArray()
  @IsOptional()
  groups?: UpdateGroupDto[];

  @IsObject()
  usersCustomersHasPermissions: UsersCustomersHasPermissionsEntity[];

  @IsOptional()
  @IsDate()
  deletedAt?: Date;
}
