import {
  Column,
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  OneToMany,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  <PERSON>inColumn,
  ManyToOne,
} from "typeorm";
import { IpEntity } from "@/modules/ips/entities/ip.entity";
import { CustomerEntity } from "@/modules/customers/entities/customer.entity";
import { UsersCustomersHasIpGroupsEntity } from "@/modules/customers/entities/users-customers-has-ip-groups.entity";

@Entity("ip_groups")
export class IpGroupEntity {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ type: "varchar", length: 255 })
  name: string;

  @Column({ type: "tinyint", default: 1 })
  is_activated: boolean | number;

  @CreateDateColumn()
  created_at: Date;

  @Column({ type: "varchar", length: 255 })
  created_by: string;

  @UpdateDateColumn()
  updated_at: Date;

  @ManyToOne(() => CustomerEntity, (customer) => customer.ipGroups, {
    nullable: false,
    onDelete: "CASCADE",
  })
  @JoinColumn({ name: "id" })
  customer: CustomerEntity;

  @Column({ type: "varchar", length: 255 })
  customers_id?: string;

  @OneToMany(() => IpEntity, (ip) => ip.group, { cascade: true })
  ips: IpEntity[];
  
  @OneToMany(() => UsersCustomersHasIpGroupsEntity,
    (customerUser) => customerUser.ipGroup, { cascade: true }
  )
  usersCustomersHasIpGroups: UsersCustomersHasIpGroupsEntity[];

  @DeleteDateColumn()
  deletedAt?: Date;
}
