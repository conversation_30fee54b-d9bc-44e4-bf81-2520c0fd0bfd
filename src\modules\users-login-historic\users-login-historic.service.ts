import { Injectable, Logger } from "@nestjs/common";
import { InjectEntityManager } from "@nestjs/typeorm";
import { EntityManager } from "typeorm";
import { UsersLoginHistoricEntity } from "./entities/users-login-historic.entity";

@Injectable()
export class UsersLoginHistoricService {
  private readonly logger = new Logger(UsersLoginHistoricService.name);

  constructor(@InjectEntityManager() private readonly manager: EntityManager) {}

  async create(
    userId: string,
    email: string,
    isMatch: boolean,
    ipAddress?: string,
    deviceInfo?: string,
    is_sso?: boolean
  ) {
    try {
      this.logger.log(
        `Creating login historic for userId: ${userId}, email: ${email}, isMatch: ${isMatch}, ipAddress: ${ipAddress}, deviceInfo: ${deviceInfo}, is_sso: ${is_sso}`
      );
      const loginHistoric = this.manager.create(UsersLoginHistoricEntity, {
        userId,
        email,
        ip: ipAddress,
        deviceInfo,
        isMatch,
        attemptedAt: new Date(),
        is_sso
      });

      await this.manager.save(loginHistoric);
    } catch (error) {
      this.logger.error("Error creating login historic", error);
      throw new Error("Error creating login historic");
    }
  }

  async isBlocked(
    email: string,
    userId: string,
    passwordAttemptLimit: number,
    loginBlockTimeMinutes: number
  ): Promise<boolean> {
    try {
      this.logger.log(
        `Checking if user with email ${email} and userId ${userId} is blocked, passwordAttemptLimit: ${passwordAttemptLimit}, loginBlockTimeMinutes: ${loginBlockTimeMinutes}`
      );
      const loginHistoric = await this.manager
        .createQueryBuilder(UsersLoginHistoricEntity, "login")
        .where("login.email = :email", { email })
        .andWhere("login.userId = :userId", { userId })
        .orderBy("login.attemptedAt", "DESC")
        .limit(passwordAttemptLimit)
        .getMany();

      const lastInvalidAttempts = loginHistoric.filter((a) => !a.isMatch);

      if (lastInvalidAttempts.length < passwordAttemptLimit) return false;

      const oldest = lastInvalidAttempts.at(-1);
      if (!oldest?.attemptedAt) return false;

      const now = Date.now();
      const blockTimeMs = loginBlockTimeMinutes * 60 * 1000;
      const diffInMs = now - new Date(oldest.attemptedAt).getTime();

      return diffInMs <= blockTimeMs;
    } catch (error) {
      this.logger.error("Error checking if user is blocked", error);
      throw new Error("Error checking if user is blocked");
    }
  }
}
