import { GenericFilter } from "@/common/filters/dtos/generic-filter.dto";
import { ApiProperty } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsOptional, IsDate } from "class-validator";

export class UserSessionFilter extends GenericFilter {
    @ApiProperty({ required: false })
    @IsOptional()
    public location?: string;

    @ApiProperty({ required: false })
    @IsOptional()
    public device?: string;

    @ApiProperty({ required: false })
    @IsOptional()
    public name?: string;

    @ApiProperty({ required: false })
    @IsDate()
    @IsOptional()
    @Transform(({ value }) => {
        const date = new Date(value)
        date.setHours(0, 0, 0, 0)
        return date
    })
    public initialDate?: Date;

    @ApiProperty({ required: false })
    @IsDate()
    @IsOptional()
    @Transform(({ value }) => {
        const date = new Date(value)
        date.setHours(23, 59, 59, 999)
        return date
    })
    public finalDate?: Date;
}
