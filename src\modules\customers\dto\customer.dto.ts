import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ptional,
  IsString,
} from "class-validator";
import { UpdateGroupDto } from "@/modules/permissions/dto/update-permission-groups.dto";
import { CustomerUserEntity } from "../entities/customer-user.entity";
import { OneToMany } from "typeorm";
import { IpGroupEntity } from "@/modules/group/entities/group.entity";

export class CustomerDto {
  @IsString()
  @IsOptional()
  id?: string;

  @IsString()
  name?: string;

  @IsNumber()
  @IsOptional()
  id_partner?: number;

  @IsArray()
  @IsOptional()
  users?: CustomerUserEntity[];

  @IsArray()
  @IsOptional()
  permissionGroups?: UpdateGroupDto[];

  @OneToMany(() => IpGroupEntity, (ipGroup) => ipGroup.customer, {
    cascade: true,
  }) // Relacionamento OneToMany com IpGroupEntity
  ipGroups: IpGroupEntity[];

  @IsDate()
  @IsOptional()
  deletedAt?: Date;
}
