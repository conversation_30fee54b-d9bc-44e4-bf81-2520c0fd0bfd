/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  UseGuards,
  HttpCode,
  Patch,
  UsePipes,
  ValidationPipe,
  Query,
  Req,
} from '@nestjs/common';
import { PermissionsService } from '@/modules/permissions/permissions.service';
import { PermissionsDto } from '@/modules/permissions/dto/permissions.dto';
import { CreateGroupDto } from '@/modules/permissions/dto/create-permission-groups.dto';
import { Jwt2faAuthGuard } from '@/common/guards/jwt-2fa/jwt-2fa-auth.guard';
import { UpdateGroupDto } from '@/modules/permissions/dto/update-permission-groups.dto';
import { ApiBearerAuth, ApiHeader, ApiResponse, ApiTags } from '@nestjs/swagger';
import { IpGroupsGuard } from '@/common/guards/rules/ip-gropus.guard';
import { PermissionsGuard } from '@/common/guards/rules/permission.guard';
import { UserDto } from '../users/dtos/user.dto';
import { FilterPermissionGroupDto } from './dto/filter-permission-group.dto';
import {
  CategoryPermissionDto,
  CreateCategoryPermissionDto,
} from './dto/category_permission.dto';
import { PermissionService } from './services/permission.service';
import { CreatePermissionDto } from './dto/create-permission.dto';
import { PermissionsEntity } from './entities/permissions.entity';
import { SubcategoryPermissionDto } from './dto/subcategory-permission.dto';
import { CreateSubcategoryPermissionDto } from './dto/create-subcategory-permission.dto';
import { PaginatedResponseDto } from '@/common/utils/pagination-response.dto';
import { GroupResponseItemDto } from './dto/group-response.dto';
import { SubcategoryPermissionEntity } from './entities/subcategory-permission.entity';

@ApiTags('Permissions')
@ApiBearerAuth('access-token')
@Controller('v1/pam/permissions')
@ApiHeader({ name: 'partner-id', required: true })
@UseGuards(PermissionsGuard)
@UseGuards(IpGroupsGuard)
export class PermissionsController {
  constructor(
    private readonly permissionsService: PermissionsService,
    private readonly permissionService: PermissionService
  ) {}

  @Get('')
  @UseGuards(Jwt2faAuthGuard)
  @HttpCode(200)
  @ApiResponse({ status: 200, description: 'Success.', type: [PermissionsDto] })
  async findAllPermissions() {
    return this.permissionsService.findAllPermissions();
  }

  @Get('sub-categories')
  @UseGuards(Jwt2faAuthGuard)
  @HttpCode(200)
  @ApiResponse({
    status: 200,
    description: 'Success.',
    type: [SubcategoryPermissionDto],
  })
  async findAllSubcategories() {
    return await this.permissionsService.findAllSubcategories();
  }

  @Get('groups/sub-categories')
  @UseGuards(Jwt2faAuthGuard)
  @HttpCode(200)
  @ApiResponse({
    status: 200,
    description: 'Success.',
    type: PaginatedResponseDto<GroupResponseItemDto>,
  })
  async findAllGroupsSubcategories(
    @Query() genericFilter: FilterPermissionGroupDto,
    @Req() req
  ): Promise<{
    data: GroupResponseItemDto[];
    totalItems: number;
    totalPages: number;
    currentPage: number;
    pageSize: number;
  }> {
    return await this.permissionsService.findAllGroupsSubcategories(
      genericFilter,
      req
    );
  }

  @Get('groups/sub-categories/:id')
  @UseGuards(Jwt2faAuthGuard)
  @UsePipes(ValidationPipe)
  @HttpCode(200)
  @ApiResponse({
    status: 200,
    description: 'Success.',
    type: [SubcategoryPermissionEntity],
  })
  async findGroupSubcategoriesById(
    @Param('id') id: string
  ): Promise<any> {
    return await this.permissionsService.findGroupSubcategoriesById(id);
  }

  @Get('groups')
  @UseGuards(Jwt2faAuthGuard)
  @HttpCode(200)
  @ApiResponse({ status: 200, description: 'Success.', type: [UpdateGroupDto] })
  async findAllGroups(
    @Query() genericFilter: FilterPermissionGroupDto
  ): Promise<{
    data: UpdateGroupDto[];
    totalItems: number;
    totalPages: number;
    currentPage: number;
    pageSize: number;
  }> {
    return await this.permissionsService.findAllGroups(genericFilter);
  }

  @Get('groups/:id')
  @UseGuards(Jwt2faAuthGuard)
  @UsePipes(ValidationPipe)
  @HttpCode(200)
  @ApiResponse({ status: 200, description: 'Success.', type: [UpdateGroupDto] })
  async findGroupById(@Param('id') id: string): Promise<UpdateGroupDto> {
    return await this.permissionsService.findOneGroupById(id);
  }

  @Get('users-permissions/:id')
  @UseGuards(Jwt2faAuthGuard)
  @UsePipes(ValidationPipe)
  @HttpCode(200)
  @ApiResponse({ status: 200, description: 'Success.', type: UserDto })
  findUserPermissions(@Param('id') id: string): Promise<UserDto> {
    return this.permissionsService.findUserWithPermissions(id);
  }

  @Get(':id')
  @UseGuards(Jwt2faAuthGuard)
  @HttpCode(200)
  @ApiResponse({ status: 200, description: 'Success.', type: PermissionsDto })
  findById(@Param('id') id: string): Promise<PermissionsDto> {
    return this.permissionsService.findOneById(id);
  }

  @Post('groups')
  @UseGuards(Jwt2faAuthGuard)
  @UsePipes(ValidationPipe)
  @HttpCode(201)
  @ApiResponse({ status: 201, description: 'Success.' })
  createGroup(@Body() createGroupDto: CreateGroupDto, @Req() req) {
    return this.permissionsService.createGroup(createGroupDto, req);
  }

  @Delete('groups/:id')
  @UseGuards(Jwt2faAuthGuard)
  @UsePipes(ValidationPipe)
  @HttpCode(200)
  @ApiResponse({ status: 200, description: 'Success.' })
  removeGroup(@Param('id') id: string) {
    return this.permissionsService.removeGroup(id);
  }

  @Patch('groups/:id')
  @UseGuards(Jwt2faAuthGuard)
  @UsePipes(ValidationPipe)
  @HttpCode(200)
  @ApiResponse({ status: 200, description: 'Success.' })
  async updateGroup(
    @Param('id') id: string,
    @Body() updateGroupDto: UpdateGroupDto
  ) {
    return await this.permissionsService.updateGroup(id, updateGroupDto);
  }

  @Patch('users-groups/:id')
  @UseGuards(Jwt2faAuthGuard)
  @UsePipes(ValidationPipe)
  @HttpCode(200)
  @ApiResponse({ status: 200, description: 'Success.' })
  async updateUserGroupPermissions(
    @Param('id') id: string,
    @Body() group: UpdateGroupDto[],
    @Req() req: any
  ) {
    return await this.permissionsService.updateGroupUsers(
      id,
      group,
      req.headers['partner-id']
    );
  }

  @Patch('users-permissions/:id')
  @UseGuards(Jwt2faAuthGuard)
  @UsePipes(ValidationPipe)
  @HttpCode(200)
  @ApiResponse({ status: 200, description: 'Success.' })
  async updateUserPermissions(
    @Param('id') id: string,
    @Body() permissions: Pick<PermissionsDto, 'id'>[],
    @Req() req: any
  ) {
    return await this.permissionsService.updatePermissionUsers(
      id,
      permissions,
      req.headers['partner-id']
    );
  }

  @Post('create/permission')
  @UseGuards(Jwt2faAuthGuard)
  @UsePipes(ValidationPipe)
  @HttpCode(201)
  @ApiResponse({ status: 201, description: 'Success.' })
  createPermission(
    @Body() createPermissionDto: CreatePermissionDto
  ): Promise<PermissionsEntity> {
    return this.permissionService.create(createPermissionDto);
  }

  @Get('list-all/categories')
  @UseGuards(Jwt2faAuthGuard)
  @UsePipes(ValidationPipe)
  @HttpCode(200)
  @ApiResponse({
    status: 200,
    description: 'Success.',
    type: CategoryPermissionDto,
  })
  async findCategories(): Promise<CategoryPermissionDto[]> {
    return await this.permissionsService.findCategories();
  }

  @Post('category')
  @UseGuards(Jwt2faAuthGuard)
  @UsePipes(ValidationPipe)
  @HttpCode(201)
  @ApiResponse({ status: 201, description: 'Success.' })
  async createCategory(@Body() createCategoryDto: CreateCategoryPermissionDto) {
    return await this.permissionsService.createCategoryPermission(
      createCategoryDto
    );
  }

  @Post('groups/sub-category')
  @UseGuards(Jwt2faAuthGuard)
  @UsePipes(ValidationPipe)
  @HttpCode(201)
  @ApiResponse({ status: 201, description: 'Success.' })
  async createSubcategory(
    @Body() createSubCategoryGroup: CreateSubcategoryPermissionDto,
    @Req() req
  ) {
    return await this.permissionsService.createGroupSubcategoryPermission(
      createSubCategoryGroup,
      req
    );
  }

  @Patch('groups/sub-category/:id')
  @UseGuards(Jwt2faAuthGuard)
  @UsePipes(ValidationPipe)
  @HttpCode(200)
  @ApiResponse({ status: 200, description: 'Success.' })
  async updateSubcategory(
    @Param('id') id: string,
    @Body() updateSubCategoryGroup: CreateSubcategoryPermissionDto,
  ) {
    return await this.permissionsService.updateGroupSubcategoryPermission(
      id,
      updateSubCategoryGroup,
    );
  }
  @Delete('category/:id')
  @UseGuards(Jwt2faAuthGuard)
  @UsePipes(ValidationPipe)
  @HttpCode(200)
  @ApiResponse({ status: 200, description: 'Success.' })
  async removeCategory(@Param('id') id: string) {
    return await this.permissionsService.deleteCategoryPermisson(id);
  }
}
