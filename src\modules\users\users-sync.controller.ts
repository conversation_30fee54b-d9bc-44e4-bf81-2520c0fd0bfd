import { Controller, Get, Query, UseGuards } from "@nestjs/common";
import { UsersService } from "./users.service";
import { Jwt2faAuthGuard } from "@/common/guards/jwt-2fa/jwt-2fa-auth.guard";

@Controller("v1/pam/")
@UseGuards(Jwt2faAuthGuard)
export class UsersSyncController {
  constructor(
    private readonly usersService: UsersService
  ) { }

  @Get("users-sync")
  async syncUsers(
    @Query('id') id?: string
  ) {
    return await this.usersService.syncUsers(id);
  }
}