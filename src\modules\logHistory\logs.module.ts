import { Modu<PERSON> } from "@nestjs/common";
import { LogsService } from "@/modules/logHistory/logs.service";
import { LogsController } from "@/modules/logHistory/logs.controller";
import { DatabaseModule } from "@/common/database/users/database.module";
import { updateLogProviders } from "@/modules/logHistory/log.provider";
import { AuthenticationModule } from "../authentication/authentication.module";
import { UsersModule } from "../users/users.module";
import { DateUtilsService } from "@/common/utils/date.utils";

@Module({
  imports: [DatabaseModule, AuthenticationModule, UsersModule],
  providers: [...updateLogProviders, LogsService, DateUtilsService],
  controllers: [LogsController],
  exports: [LogsService],
})
export class LogsModule {}
