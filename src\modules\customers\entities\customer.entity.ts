import {
  Column,
  DeleteDateColumn,
  Entity,
  OneToMany,
  PrimaryGeneratedColumn,
} from "typeorm";
import { CustomerUserEntity } from "./customer-user.entity";
import { IpGroupEntity } from "@/modules/group/entities/group.entity";

@Entity("customers")
export class CustomerEntity {
  @PrimaryGeneratedColumn("uuid")
  id?: string;

  @Column()
  name?: string;

  @Column()
  id_partner?: number;

  @OneToMany(() => CustomerUserEntity, (customerUser) => customerUser.customers)
  users?: CustomerUserEntity[];

  @DeleteDateColumn()
  deletedAt?: Date;

  @OneToMany(() => IpGroupEntity, (ipGroup) => ipGroup.customer)
  ipGroups: IpGroupEntity[];
}
