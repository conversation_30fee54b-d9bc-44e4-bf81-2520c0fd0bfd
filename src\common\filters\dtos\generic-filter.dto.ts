import { Transform } from "class-transformer";
import { <PERSON>N<PERSON><PERSON>, IsOptional, IsString } from "class-validator";
import { SortOrder } from "@/common/filters/sortOrder";

export class GenericFilter {
  @Transform(({ value }) => Number(value))
  @IsNumber({}, { message: ' "page" atrribute should be a number' })
  public page: number;

  @Transform(({ value }) => Number(value))
  @IsNumber({}, { message: ' "pageSize" attribute should be a number ' })
  public pageSize: number;

  @IsOptional()
  public orderBy?: string;

  @IsOptional()
  public sortOrder?: SortOrder = SortOrder.DESC;

  @IsString()
  @IsOptional()
  customers?: string;
}
