import { ApiProperty } from "@nestjs/swagger";
import {
    IsString,
    IsNotEmpty,
    IsOptional,
} from "class-validator";

export class CheckUserPermissionDto {
    @IsString()
    @IsNotEmpty()
    @ApiProperty({ description: "Metodo da request", example: "get post patch delete", required: true })
    method: string;

    @IsString()
    @IsNotEmpty()
    @ApiProperty({ description: "Path da request", example: "/v1/pam/", required: true })
    path: string;

    @IsOptional()
    @ApiProperty({ description: "Id do partner na api do backoffice", example: "1", required: false })
    customerPartnerId?: string;
}
