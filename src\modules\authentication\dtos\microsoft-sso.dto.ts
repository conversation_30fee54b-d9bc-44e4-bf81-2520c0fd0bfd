import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty } from 'class-validator';

export class MicrosoftSSODto {
  @ApiProperty({
    description: 'Token ID recebido da Microsoft',
    example: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIs...'
  })
  @IsString()
  @IsNotEmpty()
  microsoftToken: string;

  @ApiProperty({
    description: 'Access Token recebido da Microsoft',
    example: 'eyJ0eXAiOiJKV1QiLCJub25jZSI6InNtSGlETGZ1MHVyUDhU...'
  })
  @IsString()
  @IsNotEmpty()
  accessToken: string;
} 