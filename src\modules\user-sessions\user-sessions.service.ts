import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { InjectEntityManager } from '@nestjs/typeorm';
import { EntityManager, SelectQueryBuilder } from 'typeorm';
import { CreateUserSessionDto } from './dto/create-user-session.dto';
import { UserSessionEntity } from './entities/user-session.entity';
import { SortOrder } from '@/common/filters/sortOrder';
import { UserSessionFilter } from './dto/user-session.filter';
import { UserSessionDto } from './dto/user-session.dto';
import { getPage, getPageSize } from '@/common/utils/pagination.utils';

export interface IGetUserSessionsPaginated {
    data: UserSessionDto[];
    totalItems: number;
    totalPages: number;
    currentPage: number;
    pageSize: number;
}

@Injectable()
export class UserSessionsService {
    constructor(@InjectEntityManager() private readonly manager: EntityManager) { }

    async createUserSession({ userId, ip, device, location }: CreateUserSessionDto): Promise<string> {
        const userSession = this.manager.create(UserSessionEntity, { userId, ip, device, location })
        await this.manager.save(userSession)

        return userSession.id
    }

    async getUserSessionsPaginated(filter: UserSessionFilter): Promise<IGetUserSessionsPaginated> {
        try {
            const { page, pageSize } = this.getPagination(filter)
            const query = this.baseQuery()
            this.getOrder(filter)

            this.addFilters(query, filter)
            const queryPaginated = query
                .limit(pageSize)
                .offset((page - 1) * pageSize)
                .orderBy(filter.orderBy, filter.sortOrder)

            const [userSessions, count] = await Promise.all([
                queryPaginated.execute(),
                queryPaginated.getCount()
            ])

            const totalPages = Math.ceil(count / pageSize);

            return {
                data: userSessions as UserSessionDto[],
                totalItems: count,
                totalPages,
                currentPage: page,
                pageSize,
            };
        } catch (error) {
            console.log('Error in get user sessions:', error);
            throw new HttpException(error.message, error.status || HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    private getPagination(filter: UserSessionFilter) {
        const page = getPage(filter?.page);
        const pageSize = getPageSize(filter?.pageSize);

        return { page, pageSize }
    }

    private baseQuery(): SelectQueryBuilder<UserSessionEntity> {
        return this.manager
            .createQueryBuilder(UserSessionEntity, "user_sessions")
            .select([
                'user_sessions.id as id',
                'user_sessions.ip as ip',
                'user_sessions.location as location',
                'user_sessions.device as device',
                'user_sessions.created_at as createdAt',
                'users.name as name',
            ])
            .leftJoin('users', 'users', 'users.id = user_sessions.user_id')
    }

    private addFilters(baseQuery: SelectQueryBuilder<UserSessionEntity>, filter: UserSessionFilter): SelectQueryBuilder<UserSessionEntity> {
        if (filter.initialDate && filter.finalDate) {
            baseQuery.andWhere('created_at BETWEEN :initialDate AND :finalDate', {
                initialDate: filter.initialDate,
                finalDate: filter.finalDate,
            })
        }

        if (filter.location) {
            baseQuery.andWhere(`user_sessions.location LIKE '%${filter.location}%'`)
        }

        if (filter.device) {
            baseQuery.andWhere(`user_sessions.device LIKE '%${filter.device}%'`)
        }

        if (filter.name) {
            baseQuery.andWhere(`users.name LIKE '%${filter.name}%'`)
        }

        return baseQuery
    }

    private getOrder(filter: UserSessionFilter): void {
        if (!filter.sortOrder) {
            filter.sortOrder = SortOrder.DESC;
        }

        if (!filter.orderBy) {
            filter.orderBy = 'created_at'
        }

        if (filter.orderBy === 'name') {
            filter.orderBy = 'users.name'
        }
    }
}
