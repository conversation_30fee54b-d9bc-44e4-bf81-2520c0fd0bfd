import { HttpException, HttpStatus, Injectable } from "@nestjs/common";
import { UserDto } from "@/modules/users/dtos/user.dto";
import { UpdateUserDto } from "@/modules/users/dtos/update-user.dto";
import { LogEntity } from "@/modules/logHistory/entities/log.entity";
import { EntityManager, InsertResult, SelectQueryBuilder } from "typeorm";
import { SortOrder } from "@/common/filters/sortOrder";
import { InjectEntityManager } from "@nestjs/typeorm";
import { to } from "@/common/utils/to";
import { UserEntity } from "../users/entities/user.entity";
import { getPage, getPageSize, getTotalPage } from "@/common/utils/pagination.utils";
import { LogFilter } from "./dto/log-filter.dto";

@Injectable()
export class LogsService {
  constructor(@InjectEntityManager() private readonly manager: EntityManager) { }
  async createLog(
    updatedUser: UpdateUserDto,
    oldUser: UserDto,
    ip: string,
    userDidId: string,
    entity: string,
    changedId: string
  ): Promise<InsertResult[]> {
    const [userEntity, userError] = await to<UserEntity, Error>(
      this.manager.findOneBy(UserEntity, { id: oldUser.id })
    )
    if (userError) throw new HttpException(userError.message, HttpStatus.INTERNAL_SERVER_ERROR)
    const newUserArr = Object.entries(updatedUser);
    const timeStamp = new Date();
    const logs = newUserArr.map(async ([key, value]) => {
      const log = new LogEntity();
      log.ip = ip;
      log.field = key;
      log.oldValue = oldUser[key];
      log.newValue = value;
      log.user = userEntity;
      log.userDidId = userDidId;
      log.updatedAt = timeStamp;
      log.entity = entity;
      log.changed_id = changedId;

      const [inserted, error] = await to<InsertResult, Error>(
        this.manager.insert(LogEntity, log),
      );
      if (error)
        throw new HttpException(
          error.message,
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      return inserted;
    });
    return await Promise.all(logs);
  }

  async createLogRouteAccess(
    ip: string,
    userDidId: string,
    path,
    method,
    granted,
  ): Promise<InsertResult> {
    const timeStamp = new Date();
    const log = new LogEntity();
    log.ip = ip;
    log.field = `${method}:${path}`;
    log.oldValue = `access ${granted ? 'granted' : 'denied'}`;
    log.newValue = `access ${granted ? 'granted' : 'denied'}`;
    log.userId = userDidId;
    log.userDidId = userDidId;
    log.updatedAt = timeStamp;

    const [inserted, error] = await to<InsertResult, Error>(
      this.manager.insert(LogEntity, log),
    );
    if (error)
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    return inserted;
  }

  async getLogsPaginated(filter: LogFilter) {
    try {
      const { page, pageSize } = this.getPagination(filter)
      const query = this.baseQuery()
      this.getOrder(filter)

      this.addFilters(query, filter)
      const queryPaginated = query
        .limit(pageSize)
        .offset((page - 1) * pageSize)
        .orderBy(filter.orderBy, filter.sortOrder)

      const [data, count] = await Promise.all([
        queryPaginated.execute(),
        queryPaginated.getCount()
      ])

      const totalPages = getTotalPage(count, pageSize);

      return {
        data,
        totalItems: count,
        totalPages,
        currentPage: page,
        pageSize,
      };
    } catch (error) {
      console.log('Error in get logs:', error);
      throw new HttpException(error.message, error.status || HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  private getPagination(filter: LogFilter) {
    const page = getPage(filter?.page);
    const pageSize = getPageSize(filter?.pageSize)

    return { page, pageSize }
  }

  private baseQuery(): SelectQueryBuilder<LogEntity> {
    return this.manager
      .createQueryBuilder(LogEntity, "update_log")
      .select([
        'update_log.id as id',
        'update_log.ip as ip',
        'update_log.field as field',
        'update_log.oldValue as oldValue',
        'update_log.newValue as newValue',
        'update_log.updatedAt as updatedAt',
        'users.name as name',
        'users.userName as username',
        'users.id as userId',
        'usersDid.name as userDidName',
        'usersDid.userName as userDidUsername',
        'usersDid.id as userDidId',
      ])
      .leftJoin('users', 'users', 'users.id = update_log.userId')
      .leftJoin('users', 'usersDid', 'usersDid.id = update_log.userDidId')
  }

  private addFilters(baseQuery: SelectQueryBuilder<LogEntity>, filter: LogFilter): SelectQueryBuilder<LogEntity> {
    if (filter.initialDate && filter.finalDate) {
      baseQuery.andWhere('update_log.updatedAt BETWEEN :initialDate AND :finalDate', {
        initialDate: filter.initialDate,
        finalDate: filter.finalDate,
      })
    }

    if (filter.field) {
      baseQuery.andWhere(`update_log.field = '${filter.field}'`)
    }

    if (filter.email) {
      baseQuery.andWhere(`users.email LIKE '%${filter.email}%'`)
    }

    if (filter.id) {
      baseQuery.andWhere(`update_log.userDidId = '${filter.id}'`)
    }

    if (filter.userName) {
      baseQuery.andWhere(`users.userName LIKE '%${filter.userName}%'`)
    }

    return baseQuery
  }

  private getOrder(filter: LogFilter): void {
    if (!filter.sortOrder) {
      filter.sortOrder = SortOrder.DESC;
    }

    if (!filter.orderBy) {
      filter.orderBy = 'updatedAt'
    }

    if (filter.orderBy === 'name') {
      filter.orderBy = 'users.name'
    }

    if (filter.orderBy === 'userName') {
      filter.orderBy = 'userDidUsername'
    }

     if (filter.orderBy === 'field') {
      filter.orderBy = 'field'
    }
  }
}
