import { IsEmail, IsString, Matches } from "class-validator";

export class AuthRegisterUserDto {
  @IsString()
  name: string;

  @IsString()
  surname: string;

  @IsString()
  country: string;

  @IsEmail()
  email: string;

  @IsEmail()
  address: string;

  @Matches(/^\s*(\d{2}|\d{0})[-. ]?(\d{5}|\d{4})[-. ]?(\d{4})[-. ]?\s*$/, {
    message: "invalid phone number",
  })
  phoneNumber: string;

  /* Minimum eight characters, at least one uppercase letter, one lowercase letter, one number, and one special character */

  @Matches(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[$&+,:;=?@#|'<>.^*()%!-])[A-Za-z\d@$&+,:;=?@#|'<>.^*()%!-]{8,}$/,
    { message: "<PERSON>ha inválida" },
  )
  password: string;
}
